<?php
session_start();

// Verifica se o usuário está logado
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: index.php");
    exit;
}

require_once 'includes/db_connect.php';

$message = '';
$message_type = '';

// Lógica para buscar todos os orçamentos
$sql_select_orcamentos = "SELECT o.id, c.nome AS nome_cliente, o.data_criacao, o.valor_total, o.status_orcamento
                          FROM orcamentos o
                          LEFT JOIN clientes c ON o.cliente_id = c.id
                          ORDER BY o.data_criacao DESC";
$result_orcamentos = $conn->query($sql_select_orcamentos);

// Estatísticas dos orçamentos
$total_orcamentos = $result_orcamentos->num_rows;
$orcamentos_pendentes = 0;
$orcamentos_aprovados = 0;
$orcamentos_rejeitados = 0;
$orcamentos_convertidos = 0;
$valor_total_orcamentos = 0;

$result_orcamentos->data_seek(0);
while($row = $result_orcamentos->fetch_assoc()) {
    switch($row['status_orcamento']) {
        case 'pendente':
            $orcamentos_pendentes++;
            break;
        case 'aprovado':
            $orcamentos_aprovados++;
            $valor_total_orcamentos += $row['valor_total'];
            break;
        case 'rejeitado':
            $orcamentos_rejeitados++;
            break;
        case 'convertido_venda':
            $orcamentos_convertidos++;
            break;
    }
}

// Não fechar a conexão aqui pois ainda vamos usar no HTML
// $conn->close();

include_once 'includes/header.php';
?>

<!-- Page Header -->
<div class="page-header fade-in-up">
    <h1 class="page-title">
        <i class="fas fa-file-invoice-dollar"></i>
        Gerenciamento de Orçamentos
    </h1>
    <p class="page-subtitle">Controle seus orçamentos e propostas</p>
</div>

<?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show fade-in-up" role="alert">
        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-6 col-lg-3">
        <div class="stats-card primary fade-in-up">
            <div class="stats-icon primary">
                <i class="fas fa-file-invoice-dollar"></i>
            </div>
            <div class="stats-value"><?php echo $total_orcamentos; ?></div>
            <div class="stats-label">Total de Orçamentos</div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card warning fade-in-up">
            <div class="stats-icon warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-value"><?php echo $orcamentos_pendentes; ?></div>
            <div class="stats-label">Pendentes</div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card success fade-in-up">
            <div class="stats-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-value"><?php echo $orcamentos_aprovados; ?></div>
            <div class="stats-label">Aprovados</div>
            <div class="stats-change positive">
                <i class="fas fa-arrow-up"></i> R$ <?php echo number_format($valor_total_orcamentos, 0, ',', '.'); ?>
            </div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card info fade-in-up">
            <div class="stats-icon info">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="stats-value"><?php echo $orcamentos_convertidos; ?></div>
            <div class="stats-label">Convertidos</div>
        </div>
    </div>
</div>

<!-- Action Bar -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="modern-card fade-in-up">
            <div class="card-body-modern">
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-between align-items-md-center">
                    <div class="d-flex flex-column flex-sm-row gap-2">
                        <a href="criar_orcamento.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i> Novo Orçamento
                        </a>
                        <button class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i> Imprimir
                        </button>
                    </div>
                    <div class="d-flex gap-2 flex-grow-1 flex-md-grow-0">
                        <div class="input-group" style="max-width: 300px;">
                            <input type="text" class="form-control" placeholder="Buscar orçamentos..." id="searchInput">
                            <button class="btn btn-outline-primary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Budgets Table -->
<div class="modern-card fade-in-up">
    <div class="card-header-modern">
        <i class="fas fa-list"></i>
        Lista de Orçamentos
        <div class="ms-auto">
            <span class="badge bg-primary"><?php echo $total_orcamentos; ?> orçamentos</span>
        </div>
    </div>
    <div class="card-body-modern">
        <div class="table-responsive">
            <table class="table table-hover table-striped">
                <thead>
                    <tr>
                        <th>ID Orçamento</th>
                        <th>Cliente</th>
                        <th>Data do Orçamento</th>
                        <th>Valor Total</th>
                        <th>Status</th>
                        <th class="text-center">Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($result_orcamentos->num_rows > 0) {
                        while($row = $result_orcamentos->fetch_assoc()) {
                            // Define a classe de badge para o status
                            $status_class = '';
                            switch ($row['status_orcamento']) {
                                case 'aprovado':
                                    $status_class = 'bg-success';
                                    break;
                                case 'pendente':
                                    $status_class = 'bg-warning text-dark';
                                    break;
                                case 'rejeitado':
                                    $status_class = 'bg-danger';
                                    break;
                                case 'convertido_venda':
                                    $status_class = 'bg-info';
                                    break;
                                default:
                                    $status_class = 'bg-secondary';
                            }
                            ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['id']); ?></td>
                                <td><?php echo htmlspecialchars($row['nome_cliente']); ?></td>
                                <td><?php echo date('d/m/Y H:i', strtotime($row['data_criacao'])); ?></td>
                                <td>R$ <?php echo number_format($row['valor_total'], 2, ',', '.'); ?></td>
                                <td><span class="badge <?php echo $status_class; ?>"><?php echo htmlspecialchars(ucfirst(str_replace('_', ' ', $row['status_orcamento']))); ?></span></td>
                                <td class="text-center">
                                    <a href="detalhes_orcamento.php?id=<?php echo $row['id']; ?>" class="btn btn-info btn-sm me-1" title="Ver Detalhes">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if ($row['status_orcamento'] != 'convertido_venda' && $row['status_orcamento'] != 'rejeitado'): ?>
                                        <a href="criar_orcamento.php?id=<?php echo $row['id']; ?>" class="btn btn-primary btn-sm me-1" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    <?php endif; ?>
                                    <?php if ($row['status_orcamento'] == 'aprovado'): ?>
                                        <a href="registrar_venda.php?from_orcamento_id=<?php echo $row['id']; ?>" class="btn btn-success btn-sm" title="Converter para Venda">
                                            <i class="fas fa-exchange-alt"></i> Converter
                                        </a>
                                    <?php elseif ($row['status_orcamento'] != 'convertido_venda' && $row['status_orcamento'] != 'rejeitado'): ?>
                                        <a href="#" class="btn btn-warning btn-sm" title="Mudar Status">
                                            <i class="fas fa-sync-alt"></i> Status
                                        </a>
                                    <?php endif; ?>
                                    </td>
                            </tr>
                            <?php
                        }
                    } else {
                        echo '<tr><td colspan="6" class="text-center">Nenhum orçamento registrado ainda.</td></tr>';
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
$conn->close();
include_once 'includes/footer.php';
?>