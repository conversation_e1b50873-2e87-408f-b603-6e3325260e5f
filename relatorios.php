<?php
session_start();

// Verifica se o usuário está logado
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: index.php");
    exit;
}

require_once 'includes/db_connect.php';

$message = '';
$message_type = '';
$relatorio_gerado = false;
$dados_relatorio = [];
$titulo_relatorio = '';
$tipo_relatorio = '';

// Buscar dados para os filtros
$empresas_options = $conn->query("SELECT id, nome_empresa as nome FROM empresas_representadas ORDER BY nome_empresa ASC");
if (!$empresas_options) {
    die("Erro na consulta de empresas: " . $conn->error);
}

$produtos_options = $conn->query("SELECT id, nome FROM produtos ORDER BY nome ASC");
if (!$produtos_options) {
    die("Erro na consulta de produtos: " . $conn->error);
}

$clientes_options = $conn->query("SELECT id, nome FROM clientes ORDER BY nome ASC");
if (!$clientes_options) {
    die("Erro na consulta de clientes: " . $conn->error);
}

// Processar formulário quando enviado
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $tipo_relatorio = trim($_POST["tipo_relatorio"]);
    $data_inicio = trim($_POST["data_inicio"]);
    $data_fim = trim($_POST["data_fim"]);
    $empresa_id = !empty($_POST["empresa_id"]) ? trim($_POST["empresa_id"]) : null;
    $produto_id = !empty($_POST["produto_id"]) ? trim($_POST["produto_id"]) : null;
    $cliente_id = !empty($_POST["cliente_id"]) ? trim($_POST["cliente_id"]) : null;
    $status_filtro = !empty($_POST["status_filtro"]) ? trim($_POST["status_filtro"]) : null;

    // Validação básica
    if (empty($tipo_relatorio) || empty($data_inicio) || empty($data_fim)) {
        $message = "Por favor, selecione o tipo de relatório e o período.";
        $message_type = "danger";
    } else {
        try {
            switch ($tipo_relatorio) {
                case 'vendas_geral':
                    $dados_relatorio = gerarRelatorioVendasGeral($conn, $data_inicio, $data_fim, $empresa_id, $cliente_id, $status_filtro);
                    $titulo_relatorio = "Relatório Geral de Vendas";
                    break;

                case 'vendas_empresa':
                    if (empty($empresa_id)) {
                        throw new Exception("Selecione uma empresa para este relatório.");
                    }
                    $dados_relatorio = gerarRelatorioVendasEmpresa($conn, $data_inicio, $data_fim, $empresa_id, $status_filtro);
                    $titulo_relatorio = "Relatório de Vendas por Empresa";
                    break;

                case 'produtos_vendidos':
                    $dados_relatorio = gerarRelatorioProdutosVendidos($conn, $data_inicio, $data_fim, $empresa_id, $produto_id);
                    $titulo_relatorio = "Relatório de Produtos Vendidos";
                    break;

                case 'financeiro':
                    $dados_relatorio = gerarRelatorioFinanceiro($conn, $data_inicio, $data_fim, $empresa_id);
                    $titulo_relatorio = "Relatório Financeiro";
                    break;

                case 'clientes':
                    $dados_relatorio = gerarRelatorioClientes($conn, $data_inicio, $data_fim, $cliente_id);
                    $titulo_relatorio = "Relatório de Clientes";
                    break;

                case 'marketplace_vendas':
                    $dados_relatorio = gerarRelatorioMarketplace($conn, $data_inicio, $data_fim, $cliente_id, $status_filtro);
                    $titulo_relatorio = "Relatório de Vendas do Marketplace";
                    break;

                default:
                    throw new Exception("Tipo de relatório inválido.");
            }

            $relatorio_gerado = true;
            $message = "Relatório gerado com sucesso!";
            $message_type = "success";

        } catch (Exception $e) {
            $message = "Erro ao gerar relatório: " . $e->getMessage();
            $message_type = "danger";
        }
    }
}

// Função para gerar relatório geral de vendas
function gerarRelatorioVendasGeral($conn, $data_inicio, $data_fim, $empresa_id = null, $cliente_id = null, $status_filtro = null) {
    $sql = "SELECT v.id, v.data_venda, v.valor_total, v.status_venda, v.forma_pagamento,
                   c.nome as cliente_nome, c.email as cliente_email,
                   COUNT(iv.id) as total_itens
            FROM vendas v
            LEFT JOIN clientes c ON v.cliente_id = c.id
            LEFT JOIN itens_venda iv ON v.id = iv.venda_id
            WHERE v.data_venda BETWEEN ? AND ?";

    $params = [$data_inicio . ' 00:00:00', $data_fim . ' 23:59:59'];
    $types = "ss";

    if ($empresa_id) {
        $sql .= " AND EXISTS (SELECT 1 FROM itens_venda iv2
                             JOIN produtos p ON iv2.produto_id = p.id
                             WHERE iv2.venda_id = v.id AND p.empresa_id = ?)";
        $params[] = $empresa_id;
        $types .= "i";
    }

    if ($cliente_id) {
        $sql .= " AND v.cliente_id = ?";
        $params[] = $cliente_id;
        $types .= "i";
    }

    if ($status_filtro) {
        $sql .= " AND v.status_venda = ?";
        $params[] = $status_filtro;
        $types .= "s";
    }

    $sql .= " GROUP BY v.id ORDER BY v.data_venda DESC";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $dados = [];
    $total_vendas = 0;
    $total_valor = 0;

    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
        $total_vendas++;
        if ($row['status_venda'] == 'concluida') {
            $total_valor += $row['valor_total'];
        }
    }

    return [
        'dados' => $dados,
        'resumo' => [
            'total_vendas' => $total_vendas,
            'total_valor' => $total_valor,
            'periodo' => $data_inicio . ' a ' . $data_fim
        ]
    ];
}

// Função para gerar relatório de vendas por empresa
function gerarRelatorioVendasEmpresa($conn, $data_inicio, $data_fim, $empresa_id, $status_filtro = null) {
    $sql = "SELECT v.id, v.data_venda, v.valor_total, v.status_venda, v.forma_pagamento,
                   c.nome as cliente_nome, e.nome_empresa as empresa_nome,
                   GROUP_CONCAT(CONCAT(p.nome, ' (', iv.quantidade, 'x)') SEPARATOR ', ') as produtos
            FROM vendas v
            LEFT JOIN clientes c ON v.cliente_id = c.id
            LEFT JOIN itens_venda iv ON v.id = iv.venda_id
            LEFT JOIN produtos p ON iv.produto_id = p.id
            LEFT JOIN empresas_representadas e ON p.empresa_id = e.id
            WHERE v.data_venda BETWEEN ? AND ? AND p.empresa_id = ?";

    $params = [$data_inicio . ' 00:00:00', $data_fim . ' 23:59:59', $empresa_id];
    $types = "ssi";

    if ($status_filtro) {
        $sql .= " AND v.status_venda = ?";
        $params[] = $status_filtro;
        $types .= "s";
    }

    $sql .= " GROUP BY v.id ORDER BY v.data_venda DESC";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $dados = [];
    $total_vendas = 0;
    $total_valor = 0;
    $empresa_nome = '';

    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
        $total_vendas++;
        if ($row['status_venda'] == 'concluida') {
            $total_valor += $row['valor_total'];
        }
        if (empty($empresa_nome)) {
            $empresa_nome = $row['empresa_nome'];
        }
    }

    return [
        'dados' => $dados,
        'resumo' => [
            'total_vendas' => $total_vendas,
            'total_valor' => $total_valor,
            'empresa_nome' => $empresa_nome,
            'periodo' => $data_inicio . ' a ' . $data_fim
        ]
    ];
}

// Função para gerar relatório de produtos vendidos
function gerarRelatorioProdutosVendidos($conn, $data_inicio, $data_fim, $empresa_id = null, $produto_id = null) {
    $sql = "SELECT p.id, p.nome as produto_nome, e.nome_empresa as empresa_nome,
                   SUM(iv.quantidade) as total_vendido,
                   SUM(iv.quantidade * iv.preco_unitario) as total_faturado,
                   AVG(iv.preco_unitario) as preco_medio,
                   COUNT(DISTINCT v.id) as total_vendas
            FROM itens_venda iv
            JOIN produtos p ON iv.produto_id = p.id
            JOIN empresas_representadas e ON p.empresa_id = e.id
            JOIN vendas v ON iv.venda_id = v.id
            WHERE v.data_venda BETWEEN ? AND ? AND v.status_venda = 'concluida'";

    $params = [$data_inicio . ' 00:00:00', $data_fim . ' 23:59:59'];
    $types = "ss";

    if ($empresa_id) {
        $sql .= " AND p.empresa_id = ?";
        $params[] = $empresa_id;
        $types .= "i";
    }

    if ($produto_id) {
        $sql .= " AND p.id = ?";
        $params[] = $produto_id;
        $types .= "i";
    }

    $sql .= " GROUP BY p.id ORDER BY total_vendido DESC";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $dados = [];
    $total_produtos = 0;
    $total_quantidade = 0;
    $total_faturamento = 0;

    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
        $total_produtos++;
        $total_quantidade += $row['total_vendido'];
        $total_faturamento += $row['total_faturado'];
    }

    return [
        'dados' => $dados,
        'resumo' => [
            'total_produtos' => $total_produtos,
            'total_quantidade' => $total_quantidade,
            'total_faturamento' => $total_faturamento,
            'periodo' => $data_inicio . ' a ' . $data_fim
        ]
    ];
}

// Função para gerar relatório financeiro
function gerarRelatorioFinanceiro($conn, $data_inicio, $data_fim, $empresa_id = null) {
    $sql = "SELECT tf.id, tf.tipo, tf.valor, tf.data_transacao, tf.descricao, tf.categoria,
                   tf.referencia_id, tf.tabela_referencia
            FROM transacoes_financeiras tf
            WHERE tf.data_transacao BETWEEN ? AND ?";

    $params = [$data_inicio . ' 00:00:00', $data_fim . ' 23:59:59'];
    $types = "ss";

    if ($empresa_id) {
        $sql .= " AND (tf.empresa_id = ? OR tf.empresa_id IS NULL)";
        $params[] = $empresa_id;
        $types .= "i";
    }

    $sql .= " ORDER BY tf.data_transacao DESC";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $dados = [];
    $total_entradas = 0;
    $total_saidas = 0;
    $total_transacoes = 0;

    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
        $total_transacoes++;
        if ($row['tipo'] == 'entrada') {
            $total_entradas += $row['valor'];
        } else {
            $total_saidas += $row['valor'];
        }
    }

    return [
        'dados' => $dados,
        'resumo' => [
            'total_entradas' => $total_entradas,
            'total_saidas' => $total_saidas,
            'saldo' => $total_entradas - $total_saidas,
            'total_transacoes' => $total_transacoes,
            'periodo' => $data_inicio . ' a ' . $data_fim
        ]
    ];
}

// Função para gerar relatório de clientes
function gerarRelatorioMarketplace($conn, $data_inicio, $data_fim, $cliente_id = null, $status_filtro = null) {
    $sql = "SELECT mp.id, mp.numero_pedido, mp.data_pedido, mp.valor_total, mp.status_pedido, mp.tipo_faturamento,
                   mp.data_vencimento, mp.data_entrega_agendada, mp.data_confirmacao,
                   c.nome as cliente_nome, c.email as cliente_email,
                   v.id as venda_id, tf.id as transacao_id,
                   COUNT(mip.id) as total_itens
            FROM marketplace_pedidos mp
            LEFT JOIN clientes c ON mp.cliente_id = c.id
            LEFT JOIN vendas v ON mp.venda_id = v.id
            LEFT JOIN transacoes_financeiras tf ON mp.transacao_financeira_id = tf.id
            LEFT JOIN marketplace_itens_pedido mip ON mp.id = mip.pedido_id
            WHERE DATE(mp.data_pedido) BETWEEN ? AND ?";

    $params = [$data_inicio, $data_fim];
    $types = "ss";

    if (!empty($cliente_id)) {
        $sql .= " AND mp.cliente_id = ?";
        $params[] = $cliente_id;
        $types .= "i";
    }

    if (!empty($status_filtro)) {
        $sql .= " AND mp.status_pedido = ?";
        $params[] = $status_filtro;
        $types .= "s";
    }

    $sql .= " GROUP BY mp.id ORDER BY mp.data_pedido DESC";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $dados = [];
    $total_pedidos = 0;
    $total_valor = 0;
    $pedidos_integrados = 0;

    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
        $total_pedidos++;
        $total_valor += $row['valor_total'];

        if ($row['venda_id'] && $row['transacao_id']) {
            $pedidos_integrados++;
        }
    }

    // Estatísticas por status
    $sql_stats = "SELECT status_pedido, COUNT(*) as quantidade, SUM(valor_total) as valor_total
                  FROM marketplace_pedidos
                  WHERE DATE(data_pedido) BETWEEN ? AND ?";

    $params_stats = [$data_inicio, $data_fim];
    $types_stats = "ss";

    if (!empty($cliente_id)) {
        $sql_stats .= " AND cliente_id = ?";
        $params_stats[] = $cliente_id;
        $types_stats .= "i";
    }

    $sql_stats .= " GROUP BY status_pedido";

    $stmt_stats = $conn->prepare($sql_stats);
    $stmt_stats->bind_param($types_stats, ...$params_stats);
    $stmt_stats->execute();
    $result_stats = $stmt_stats->get_result();

    $stats_por_status = [];
    while ($row = $result_stats->fetch_assoc()) {
        $stats_por_status[$row['status_pedido']] = $row;
    }

    return [
        'dados' => $dados,
        'resumo' => [
            'total_pedidos' => $total_pedidos,
            'total_valor' => $total_valor,
            'pedidos_integrados' => $pedidos_integrados,
            'taxa_integracao' => $total_pedidos > 0 ? round(($pedidos_integrados / $total_pedidos) * 100, 1) : 0,
            'periodo' => date('d/m/Y', strtotime($data_inicio)) . ' a ' . date('d/m/Y', strtotime($data_fim)),
            'stats_por_status' => $stats_por_status
        ]
    ];
}

function gerarRelatorioClientes($conn, $data_inicio, $data_fim, $cliente_id = null) {
    $sql = "SELECT c.id, c.nome, c.email, c.telefone,
                   COUNT(v.id) as total_vendas,
                   SUM(CASE WHEN v.status_venda = 'concluida' THEN v.valor_total ELSE 0 END) as total_comprado,
                   MAX(v.data_venda) as ultima_compra,
                   AVG(CASE WHEN v.status_venda = 'concluida' THEN v.valor_total ELSE NULL END) as ticket_medio
            FROM clientes c
            LEFT JOIN vendas v ON c.id = v.cliente_id AND v.data_venda BETWEEN ? AND ?";

    $params = [$data_inicio . ' 00:00:00', $data_fim . ' 23:59:59'];
    $types = "ss";

    if ($cliente_id) {
        $sql .= " WHERE c.id = ?";
        $params[] = $cliente_id;
        $types .= "i";
    }

    $sql .= " GROUP BY c.id ORDER BY total_comprado DESC";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $dados = [];
    $total_clientes = 0;
    $clientes_ativos = 0;
    $faturamento_total = 0;

    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
        $total_clientes++;
        if ($row['total_vendas'] > 0) {
            $clientes_ativos++;
        }
        $faturamento_total += $row['total_comprado'];
    }

    return [
        'dados' => $dados,
        'resumo' => [
            'total_clientes' => $total_clientes,
            'clientes_ativos' => $clientes_ativos,
            'faturamento_total' => $faturamento_total,
            'ticket_medio_geral' => $clientes_ativos > 0 ? $faturamento_total / $clientes_ativos : 0,
            'periodo' => $data_inicio . ' a ' . $data_fim
        ]
    ];
}

// Não fechar a conexão aqui pois ainda vamos usar no HTML
// $conn->close();

include_once 'includes/header.php';
?>

<!-- Page Header -->
<div class="page-header fade-in-up">
    <h1 class="page-title">
        <i class="fas fa-chart-bar"></i>
        Relatórios
    </h1>
    <p class="page-subtitle">
        Gere relatórios detalhados de vendas, produtos, clientes e financeiro
    </p>
</div>

<?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show fade-in-up" role="alert">
        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : ($message_type == 'info' ? 'info-circle' : 'exclamation-triangle'); ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Formulário de Filtros -->
<div class="modern-card fade-in-up">
    <div class="card-header-modern">
        <i class="fas fa-filter"></i>
        Filtros do Relatório
    </div>
    <div class="card-body-modern">
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
            <div class="row g-4">
                <!-- Tipo de Relatório -->
                <div class="col-12">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-file-alt me-2"></i>Tipo de Relatório
                    </h5>
                </div>

                <div class="col-md-6">
                    <label for="tipo_relatorio" class="form-label">Selecione o Relatório *</label>
                    <select class="form-control" id="tipo_relatorio" name="tipo_relatorio" required>
                        <option value="">Escolha um tipo de relatório...</option>
                        <option value="vendas_geral" <?php echo ($tipo_relatorio == 'vendas_geral' ? 'selected' : ''); ?>>
                            📊 Vendas Geral
                        </option>
                        <option value="vendas_empresa" <?php echo ($tipo_relatorio == 'vendas_empresa' ? 'selected' : ''); ?>>
                            🏢 Vendas por Empresa
                        </option>
                        <option value="produtos_vendidos" <?php echo ($tipo_relatorio == 'produtos_vendidos' ? 'selected' : ''); ?>>
                            📦 Produtos Vendidos
                        </option>
                        <option value="financeiro" <?php echo ($tipo_relatorio == 'financeiro' ? 'selected' : ''); ?>>
                            💰 Relatório Financeiro
                        </option>
                        <option value="clientes" <?php echo ($tipo_relatorio == 'clientes' ? 'selected' : ''); ?>>
                            👥 Relatório de Clientes
                        </option>
                        <option value="marketplace_vendas" <?php echo ($tipo_relatorio == 'marketplace_vendas' ? 'selected' : ''); ?>>
                            🛒 Vendas do Marketplace
                        </option>
                    </select>
                </div>

                <!-- Período -->
                <div class="col-12">
                    <h5 class="text-primary mb-3 mt-4">
                        <i class="fas fa-calendar-alt me-2"></i>Período
                    </h5>
                </div>

                <div class="col-md-3">
                    <label for="data_inicio" class="form-label">Data Início *</label>
                    <input type="date" class="form-control" id="data_inicio" name="data_inicio"
                           value="<?php echo isset($data_inicio) ? $data_inicio : date('Y-m-01'); ?>" required>
                </div>

                <div class="col-md-3">
                    <label for="data_fim" class="form-label">Data Fim *</label>
                    <input type="date" class="form-control" id="data_fim" name="data_fim"
                           value="<?php echo isset($data_fim) ? $data_fim : date('Y-m-d'); ?>" required>
                </div>

                <!-- Filtros Opcionais -->
                <div class="col-12">
                    <h5 class="text-primary mb-3 mt-4">
                        <i class="fas fa-sliders-h me-2"></i>Filtros Opcionais
                    </h5>
                </div>

                <div class="col-md-3">
                    <label for="empresa_id" class="form-label">Empresa</label>
                    <select class="form-control" id="empresa_id" name="empresa_id">
                        <option value="">Todas as empresas</option>
                        <?php
                        if ($empresas_options && $empresas_options->num_rows > 0) {
                            $empresas_options->data_seek(0);
                            while($empresa = $empresas_options->fetch_assoc()) {
                                $selected = (isset($empresa_id) && $empresa['id'] == $empresa_id) ? 'selected' : '';
                                echo '<option value="' . htmlspecialchars($empresa['id']) . '" ' . $selected . '>' . htmlspecialchars($empresa['nome']) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="produto_id" class="form-label">Produto</label>
                    <select class="form-control" id="produto_id" name="produto_id">
                        <option value="">Todos os produtos</option>
                        <?php
                        if ($produtos_options && $produtos_options->num_rows > 0) {
                            $produtos_options->data_seek(0);
                            while($produto = $produtos_options->fetch_assoc()) {
                                $selected = (isset($produto_id) && $produto['id'] == $produto_id) ? 'selected' : '';
                                echo '<option value="' . htmlspecialchars($produto['id']) . '" ' . $selected . '>' . htmlspecialchars($produto['nome']) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="cliente_id" class="form-label">Cliente</label>
                    <select class="form-control" id="cliente_id" name="cliente_id">
                        <option value="">Todos os clientes</option>
                        <?php
                        if ($clientes_options && $clientes_options->num_rows > 0) {
                            $clientes_options->data_seek(0);
                            while($cliente = $clientes_options->fetch_assoc()) {
                                $selected = (isset($cliente_id) && $cliente['id'] == $cliente_id) ? 'selected' : '';
                                echo '<option value="' . htmlspecialchars($cliente['id']) . '" ' . $selected . '>' . htmlspecialchars($cliente['nome']) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="status_filtro" class="form-label">Status</label>
                    <select class="form-control" id="status_filtro" name="status_filtro">
                        <option value="">Todos os status</option>
                        <option value="pendente" <?php echo (isset($status_filtro) && $status_filtro == 'pendente' ? 'selected' : ''); ?>>Pendente</option>
                        <option value="concluida" <?php echo (isset($status_filtro) && $status_filtro == 'concluida' ? 'selected' : ''); ?>>Concluída</option>
                        <option value="cancelada" <?php echo (isset($status_filtro) && $status_filtro == 'cancelada' ? 'selected' : ''); ?>>Cancelada</option>
                    </select>
                </div>

                <!-- Botão -->
                <div class="col-12">
                    <div class="d-flex gap-2 justify-content-end mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-chart-line me-2"></i>Gerar Relatório
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<?php if ($relatorio_gerado && !empty($dados_relatorio)): ?>
<!-- Relatório Gerado -->
<div class="modern-card fade-in-up mt-4">
    <div class="card-header-modern">
        <i class="fas fa-chart-line"></i>
        <?php echo $titulo_relatorio; ?>
        <div class="ms-auto">
            <button onclick="window.print()" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-print me-1"></i>Imprimir
            </button>
            <button onclick="exportarCSV()" class="btn btn-outline-success btn-sm">
                <i class="fas fa-file-csv me-1"></i>Exportar CSV
            </button>
        </div>
    </div>
    <div class="card-body-modern">

        <!-- Cards de Resumo -->
        <div class="row g-4 mb-4">
            <?php if ($tipo_relatorio == 'vendas_geral' || $tipo_relatorio == 'vendas_empresa'): ?>
                <div class="col-6 col-lg-3">
                    <div class="stats-card primary">
                        <div class="stats-icon primary">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stats-value"><?php echo $dados_relatorio['resumo']['total_vendas']; ?></div>
                        <div class="stats-label">Total de Vendas</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card success">
                        <div class="stats-icon success">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stats-value">R$ <?php echo number_format($dados_relatorio['resumo']['total_valor'], 2, ',', '.'); ?></div>
                        <div class="stats-label">Faturamento</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card info">
                        <div class="stats-icon info">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="stats-value"><?php echo $dados_relatorio['resumo']['periodo']; ?></div>
                        <div class="stats-label">Período</div>
                    </div>
                </div>
                <?php if (isset($dados_relatorio['resumo']['empresa_nome'])): ?>
                <div class="col-6 col-lg-3">
                    <div class="stats-card warning">
                        <div class="stats-icon warning">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="stats-value"><?php echo htmlspecialchars($dados_relatorio['resumo']['empresa_nome']); ?></div>
                        <div class="stats-label">Empresa</div>
                    </div>
                </div>
                <?php endif; ?>

            <?php elseif ($tipo_relatorio == 'produtos_vendidos'): ?>
                <div class="col-6 col-lg-3">
                    <div class="stats-card primary">
                        <div class="stats-icon primary">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="stats-value"><?php echo $dados_relatorio['resumo']['total_produtos']; ?></div>
                        <div class="stats-label">Produtos Vendidos</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card info">
                        <div class="stats-icon info">
                            <i class="fas fa-sort-numeric-up"></i>
                        </div>
                        <div class="stats-value"><?php echo $dados_relatorio['resumo']['total_quantidade']; ?></div>
                        <div class="stats-label">Quantidade Total</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card success">
                        <div class="stats-icon success">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stats-value">R$ <?php echo number_format($dados_relatorio['resumo']['total_faturamento'], 2, ',', '.'); ?></div>
                        <div class="stats-label">Faturamento</div>
                    </div>
                </div>

            <?php elseif ($tipo_relatorio == 'financeiro'): ?>
                <div class="col-6 col-lg-3">
                    <div class="stats-card success">
                        <div class="stats-icon success">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="stats-value">R$ <?php echo number_format($dados_relatorio['resumo']['total_entradas'], 2, ',', '.'); ?></div>
                        <div class="stats-label">Entradas</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card danger">
                        <div class="stats-icon danger">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="stats-value">R$ <?php echo number_format($dados_relatorio['resumo']['total_saidas'], 2, ',', '.'); ?></div>
                        <div class="stats-label">Saídas</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card <?php echo ($dados_relatorio['resumo']['saldo'] >= 0 ? 'primary' : 'warning'); ?>">
                        <div class="stats-icon <?php echo ($dados_relatorio['resumo']['saldo'] >= 0 ? 'primary' : 'warning'); ?>">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                        <div class="stats-value">R$ <?php echo number_format($dados_relatorio['resumo']['saldo'], 2, ',', '.'); ?></div>
                        <div class="stats-label">Saldo</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card info">
                        <div class="stats-icon info">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="stats-value"><?php echo $dados_relatorio['resumo']['total_transacoes']; ?></div>
                        <div class="stats-label">Transações</div>
                    </div>
                </div>

            <?php elseif ($tipo_relatorio == 'clientes'): ?>
                <div class="col-6 col-lg-3">
                    <div class="stats-card primary">
                        <div class="stats-icon primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stats-value"><?php echo $dados_relatorio['resumo']['total_clientes']; ?></div>
                        <div class="stats-label">Total Clientes</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card success">
                        <div class="stats-icon success">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stats-value"><?php echo $dados_relatorio['resumo']['clientes_ativos']; ?></div>
                        <div class="stats-label">Clientes Ativos</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card info">
                        <div class="stats-icon info">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stats-value">R$ <?php echo number_format($dados_relatorio['resumo']['faturamento_total'], 2, ',', '.'); ?></div>
                        <div class="stats-label">Faturamento Total</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card warning">
                        <div class="stats-icon warning">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <div class="stats-value">R$ <?php echo number_format($dados_relatorio['resumo']['ticket_medio_geral'], 2, ',', '.'); ?></div>
                        <div class="stats-label">Ticket Médio</div>
                    </div>
                </div>

            <?php elseif ($tipo_relatorio == 'marketplace_vendas'): ?>
                <div class="col-6 col-lg-3">
                    <div class="stats-card primary">
                        <div class="stats-icon primary">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stats-value"><?php echo $dados_relatorio['resumo']['total_pedidos']; ?></div>
                        <div class="stats-label">Total de Pedidos</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card success">
                        <div class="stats-icon success">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stats-value">R$ <?php echo number_format($dados_relatorio['resumo']['total_valor'], 2, ',', '.'); ?></div>
                        <div class="stats-label">Faturamento</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card info">
                        <div class="stats-icon info">
                            <i class="fas fa-link"></i>
                        </div>
                        <div class="stats-value"><?php echo $dados_relatorio['resumo']['pedidos_integrados']; ?></div>
                        <div class="stats-label">Pedidos Integrados</div>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stats-card warning">
                        <div class="stats-icon warning">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stats-value"><?php echo $dados_relatorio['resumo']['taxa_integracao']; ?>%</div>
                        <div class="stats-label">Taxa de Integração</div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Tabela de Dados -->
        <div class="table-modern">
            <table class="table table-hover mb-0" id="tabelaRelatorio">
                <thead>
                    <tr>
                        <?php if ($tipo_relatorio == 'vendas_geral' || $tipo_relatorio == 'vendas_empresa'): ?>
                            <th>ID</th>
                            <th>Data</th>
                            <th>Cliente</th>
                            <th>Valor</th>
                            <th>Status</th>
                            <th>Pagamento</th>
                            <th>Itens</th>
                            <?php if ($tipo_relatorio == 'vendas_empresa'): ?>
                                <th>Produtos</th>
                            <?php endif; ?>
                        <?php elseif ($tipo_relatorio == 'produtos_vendidos'): ?>
                            <th>Produto</th>
                            <th>Empresa</th>
                            <th>Qtd Vendida</th>
                            <th>Preço Médio</th>
                            <th>Faturamento</th>
                            <th>Nº Vendas</th>
                        <?php elseif ($tipo_relatorio == 'financeiro'): ?>
                            <th>ID</th>
                            <th>Tipo</th>
                            <th>Valor</th>
                            <th>Data</th>
                            <th>Descrição</th>
                            <th>Categoria</th>
                            <th>Referência</th>
                        <?php elseif ($tipo_relatorio == 'clientes'): ?>
                            <th>Cliente</th>
                            <th>Email</th>
                            <th>Telefone</th>
                            <th>Vendas</th>
                            <th>Total Comprado</th>
                            <th>Última Compra</th>
                            <th>Ticket Médio</th>
                        <?php elseif ($tipo_relatorio == 'marketplace_vendas'): ?>
                            <th>Pedido</th>
                            <th>Cliente</th>
                            <th>Data</th>
                            <th>Valor</th>
                            <th>Status</th>
                            <th>Faturamento</th>
                            <th>Integração</th>
                            <th>Itens</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($dados_relatorio['dados'])): ?>
                        <?php foreach ($dados_relatorio['dados'] as $row): ?>
                            <tr>
                                <?php if ($tipo_relatorio == 'vendas_geral' || $tipo_relatorio == 'vendas_empresa'): ?>
                                    <td><span class="text-muted">#<?php echo $row['id']; ?></span></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($row['data_venda'])); ?></td>
                                    <td><?php echo htmlspecialchars($row['cliente_nome']); ?></td>
                                    <td class="fw-bold">R$ <?php echo number_format($row['valor_total'], 2, ',', '.'); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo ($row['status_venda'] == 'concluida') ? 'success' : (($row['status_venda'] == 'pendente') ? 'warning' : 'danger'); ?>">
                                            <?php echo ucfirst($row['status_venda']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($row['forma_pagamento']); ?></td>
                                    <td><span class="badge bg-light text-dark"><?php echo $row['total_itens']; ?> itens</span></td>
                                    <?php if ($tipo_relatorio == 'vendas_empresa' && isset($row['produtos'])): ?>
                                        <td><small><?php echo htmlspecialchars($row['produtos']); ?></small></td>
                                    <?php endif; ?>

                                <?php elseif ($tipo_relatorio == 'produtos_vendidos'): ?>
                                    <td><?php echo htmlspecialchars($row['produto_nome']); ?></td>
                                    <td><?php echo htmlspecialchars($row['empresa_nome']); ?></td>
                                    <td class="fw-bold"><?php echo $row['total_vendido']; ?></td>
                                    <td>R$ <?php echo number_format($row['preco_medio'], 2, ',', '.'); ?></td>
                                    <td class="fw-bold text-success">R$ <?php echo number_format($row['total_faturado'], 2, ',', '.'); ?></td>
                                    <td><span class="badge bg-primary"><?php echo $row['total_vendas']; ?></span></td>

                                <?php elseif ($tipo_relatorio == 'financeiro'): ?>
                                    <td><span class="text-muted">#<?php echo $row['id']; ?></span></td>
                                    <td>
                                        <span class="badge bg-<?php echo ($row['tipo'] == 'entrada') ? 'success' : 'danger'; ?>">
                                            <i class="fas fa-arrow-<?php echo ($row['tipo'] == 'entrada') ? 'up' : 'down'; ?> me-1"></i><?php echo ucfirst($row['tipo']); ?>
                                        </span>
                                    </td>
                                    <td class="fw-bold <?php echo ($row['tipo'] == 'entrada') ? 'text-success' : 'text-danger'; ?>">
                                        R$ <?php echo number_format($row['valor'], 2, ',', '.'); ?>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($row['data_transacao'])); ?></td>
                                    <td><?php echo htmlspecialchars($row['descricao']); ?></td>
                                    <td>
                                        <?php if (!empty($row['categoria'])): ?>
                                            <span class="badge bg-light text-dark"><?php echo htmlspecialchars($row['categoria']); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($row['referencia_id']) && !empty($row['tabela_referencia'])): ?>
                                            <small><?php echo ucfirst($row['tabela_referencia']); ?> #<?php echo $row['referencia_id']; ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>

                                <?php elseif ($tipo_relatorio == 'clientes'): ?>
                                    <td><?php echo htmlspecialchars($row['nome']); ?></td>
                                    <td><?php echo htmlspecialchars($row['email']); ?></td>
                                    <td><?php echo htmlspecialchars($row['telefone']); ?></td>
                                    <td><span class="badge bg-primary"><?php echo $row['total_vendas']; ?></span></td>
                                    <td class="fw-bold text-success">R$ <?php echo number_format($row['total_comprado'], 2, ',', '.'); ?></td>
                                    <td>
                                        <?php if ($row['ultima_compra']): ?>
                                            <?php echo date('d/m/Y', strtotime($row['ultima_compra'])); ?>
                                        <?php else: ?>
                                            <span class="text-muted">Nunca</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($row['ticket_medio']): ?>
                                            R$ <?php echo number_format($row['ticket_medio'], 2, ',', '.'); ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>

                                <?php elseif ($tipo_relatorio == 'marketplace_vendas'): ?>
                                    <td>
                                        <strong>#<?php echo htmlspecialchars($row['numero_pedido']); ?></strong><br>
                                        <small class="text-muted">ID: <?php echo $row['id']; ?></small>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($row['cliente_nome']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($row['cliente_email']); ?></small>
                                    </td>
                                    <td>
                                        <?php echo date('d/m/Y H:i', strtotime($row['data_pedido'])); ?><br>
                                        <?php if ($row['data_confirmacao']): ?>
                                            <small class="text-success">Conf: <?php echo date('d/m/Y H:i', strtotime($row['data_confirmacao'])); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="fw-bold">R$ <?php echo number_format($row['valor_total'], 2, ',', '.'); ?></td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        switch ($row['status_pedido']) {
                                            case 'confirmado':
                                            case 'preparando':
                                                $status_class = 'bg-info';
                                                break;
                                            case 'pendente':
                                                $status_class = 'bg-warning text-dark';
                                                break;
                                            case 'entregue':
                                                $status_class = 'bg-success';
                                                break;
                                            case 'cancelado':
                                                $status_class = 'bg-danger';
                                                break;
                                            default:
                                                $status_class = 'bg-secondary';
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $row['status_pedido'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        switch ($row['tipo_faturamento']) {
                                            case 'avista': echo 'À Vista'; break;
                                            case '15_dias': echo '15 dias'; break;
                                            case '20_dias': echo '20 dias'; break;
                                            case '30_dias': echo '30 dias'; break;
                                        }
                                        ?><br>
                                        <?php if ($row['data_vencimento']): ?>
                                            <small class="text-muted">Venc: <?php echo date('d/m/Y', strtotime($row['data_vencimento'])); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($row['venda_id'] && $row['transacao_id']): ?>
                                            <span class="badge bg-success" title="Totalmente integrado">
                                                <i class="fas fa-check"></i> Integrado
                                            </span>
                                        <?php elseif ($row['status_pedido'] == 'confirmado'): ?>
                                            <span class="badge bg-warning text-dark" title="Confirmado mas não integrado">
                                                <i class="fas fa-exclamation-triangle"></i> Pendente
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary" title="Aguardando confirmação">
                                                <i class="fas fa-clock"></i> Aguardando
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark"><?php echo $row['total_itens']; ?> itens</span>
                                    </td>
                                <?php endif; ?>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="10" class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                Nenhum dado encontrado para os filtros selecionados.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function exportarCSV() {
    const table = document.getElementById('tabelaRelatorio');
    let csv = [];

    // Cabeçalhos
    const headers = [];
    table.querySelectorAll('thead th').forEach(th => {
        headers.push(th.textContent.trim());
    });
    csv.push(headers.join(','));

    // Dados
    table.querySelectorAll('tbody tr').forEach(tr => {
        const row = [];
        tr.querySelectorAll('td').forEach(td => {
            row.push('"' + td.textContent.trim().replace(/"/g, '""') + '"');
        });
        if (row.length > 0) {
            csv.push(row.join(','));
        }
    });

    // Download
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'relatorio_<?php echo $tipo_relatorio; ?>_<?php echo date('Y-m-d'); ?>.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>

<?php
$conn->close();
include_once 'includes/footer.php';
?>
