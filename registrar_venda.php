<?php
session_start();

// Verifica se o usuário está logado
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: index.php");
    exit;
}

require_once 'includes/db_connect.php';

$venda_id = $cliente_id = $data_venda = $valor_total = $forma_pagamento = $status_venda = "";
$title = "Registrar Nova Venda";
$submit_button_text = "Registrar Venda";
$message = '';
$message_type = '';
$itens_da_venda = []; // Array para armazenar os produtos da venda (para edição)

// Buscar todos os clientes para o campo SELECT
$clientes_options = $conn->query("SELECT id, nome FROM clientes ORDER BY nome ASC");

// Buscar todos os produtos para o campo SELECT na adição de itens
$produtos_options = $conn->query("SELECT id, nome, preco_venda, quantidade_estoque FROM produtos ORDER BY nome ASC");

// Processar formulário quando enviado
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $venda_id = trim($_POST["venda_id"] ?? '');
    $cliente_id = trim($_POST["cliente_id"]);
    $forma_pagamento = trim($_POST["forma_pagamento"]);
    $status_venda = trim($_POST["status_venda"]);
    $itens_selecionados_json = $_POST["itens_selecionados_json"] ?? '[]'; // Itens da venda em JSON

    // Decodifica os itens selecionados (que vêm do campo oculto do formulário)
    $itens_da_venda_post = json_decode($itens_selecionados_json, true);

    // Recalcula o valor total com base nos itens enviados (segurança)
    $calculated_valor_total = 0;
    foreach ($itens_da_venda_post as $item) {
        $calculated_valor_total += ($item['preco_unitario'] * $item['quantidade']);
    }
    $valor_total = $calculated_valor_total; // Usamos o valor recalculado

    // Validação básica
    if (empty($cliente_id) || empty($forma_pagamento) || empty($status_venda) || empty($itens_da_venda_post)) {
        $message = "Por favor, preencha todos os campos obrigatórios e adicione pelo menos um produto à venda.";
        $message_type = "danger";
    } else {
        $conn->begin_transaction(); // Inicia transação para garantir atomicidade

        try {
            if (empty($venda_id)) { // Nova Venda
                $sql = "INSERT INTO vendas (cliente_id, valor_total, forma_pagamento, status_venda) VALUES (?, ?, ?, ?)";
                if ($stmt = $conn->prepare($sql)) {
                    $stmt->bind_param("idss", $cliente_id, $valor_total, $forma_pagamento, $status_venda);
                    if (!$stmt->execute()) {
                        throw new Exception("Erro ao registrar venda: " . $stmt->error);
                    }
                    $venda_id = $conn->insert_id; // Pega o ID da venda recém-criada
                    $stmt->close();
                } else {
                    throw new Exception("Erro na preparação da query de inserção da venda: " . $conn->error);
                }
            } else { // Editar Venda Existente
                // Primeiramente, reverte o estoque dos itens antigos (se houver) para evitar estoque incorreto
                $sql_old_items = "SELECT produto_id, quantidade FROM itens_venda WHERE venda_id = ?";
                $stmt_old_items = $conn->prepare($sql_old_items);
                $stmt_old_items->bind_param("i", $venda_id);
                $stmt_old_items->execute();
                $result_old_items = $stmt_old_items->get_result();
                while ($old_item = $result_old_items->fetch_assoc()) {
                    $sql_revert_stock = "UPDATE produtos SET quantidade_estoque = quantidade_estoque + ? WHERE id = ?";
                    $stmt_revert_stock = $conn->prepare($sql_revert_stock);
                    $stmt_revert_stock->bind_param("ii", $old_item['quantidade'], $old_item['produto_id']);
                    $stmt_revert_stock->execute();
                    $stmt_revert_stock->close();
                }
                $stmt_old_items->close();

                // Exclui os itens antigos da venda para inserir os novos
                $sql_delete_old_items = "DELETE FROM itens_venda WHERE venda_id = ?";
                $stmt_delete_old_items = $conn->prepare($sql_delete_old_items);
                $stmt_delete_old_items->bind_param("i", $venda_id);
                if (!$stmt_delete_old_items->execute()) {
                    throw new Exception("Erro ao deletar itens antigos da venda: " . $stmt_delete_old_items->error);
                }
                $stmt_delete_old_items->close();

                // Atualiza os dados da venda
                $sql = "UPDATE vendas SET cliente_id = ?, valor_total = ?, forma_pagamento = ?, status_venda = ? WHERE id = ?";
                if ($stmt = $conn->prepare($sql)) {
                    $stmt->bind_param("idssi", $cliente_id, $valor_total, $forma_pagamento, $status_venda, $venda_id);
                    if (!$stmt->execute()) {
                        throw new Exception("Erro ao atualizar venda: " . $stmt->error);
                    }
                    $stmt->close();
                } else {
                    throw new Exception("Erro na preparação da query de atualização da venda: " . $conn->error);
                }
            }

            // Inserir/Atualizar Itens da Venda e dar baixa no estoque
            foreach ($itens_da_venda_post as $item) {
                $produto_id = $item['id'];
                $quantidade = $item['quantidade'];
                $preco_unitario = $item['preco_unitario'];

                // Verifica se há estoque suficiente antes de dar baixa (apenas para novas vendas ou se a quantidade aumentou)
                // Para edição, a lógica de reverter estoque antigo e dar baixa no novo já é suficiente.
                if (empty($venda_id_get)) { // Se for uma nova venda (não uma edição)
                    $sql_check_stock = "SELECT quantidade_estoque FROM produtos WHERE id = ?";
                    $stmt_check_stock = $conn->prepare($sql_check_stock);
                    $stmt_check_stock->bind_param("i", $produto_id);
                    $stmt_check_stock->execute();
                    $result_check_stock = $stmt_check_stock->get_result();
                    $current_stock = $result_check_stock->fetch_assoc()['quantidade_estoque'];
                    $stmt_check_stock->close();

                    if ($current_stock < $quantidade) {
                        throw new Exception("Estoque insuficiente para o produto ID " . $produto_id . ". Disponível: " . $current_stock);
                    }
                }


                // Insere o item na tabela itens_venda
                $sql_item = "INSERT INTO itens_venda (venda_id, produto_id, quantidade, preco_unitario) VALUES (?, ?, ?, ?)";
                if ($stmt_item = $conn->prepare($sql_item)) {
                    $stmt_item->bind_param("iiid", $venda_id, $produto_id, $quantidade, $preco_unitario);
                    if (!$stmt_item->execute()) {
                        throw new Exception("Erro ao inserir item da venda: " . $stmt_item->error);
                    }
                    $stmt_item->close();
                } else {
                    throw new Exception("Erro na preparação da query de item da venda: " . $conn->error);
                }

                // Dá baixa no estoque
                $sql_update_stock = "UPDATE produtos SET quantidade_estoque = quantidade_estoque - ? WHERE id = ?";
                if ($stmt_stock = $conn->prepare($sql_update_stock)) {
                    $stmt_stock->bind_param("ii", $quantidade, $produto_id);
                    if (!$stmt_stock->execute()) {
                        throw new Exception("Erro ao dar baixa no estoque: " . $stmt_stock->error);
                    }
                    $stmt_stock->close();
                } else {
                    throw new Exception("Erro na preparação da query de atualização de estoque: " . $conn->error);
                }
            }

            // Registrar transação financeira para vendas concluídas
            if ($status_venda == 'concluida') {
                // Verificar se já existe uma transação para esta venda
                $sql_check_transacao = "SELECT id FROM transacoes_financeiras WHERE referencia_id = ? AND tabela_referencia = 'vendas'";
                $stmt_check = $conn->prepare($sql_check_transacao);
                $stmt_check->bind_param("i", $venda_id);
                $stmt_check->execute();
                $result_check = $stmt_check->get_result();

                if ($result_check->num_rows == 0) {
                    // Não existe transação, criar nova
                    $descricao_transacao = "Receita da Venda #" . $venda_id;
                    $categoria_transacao = "Vendas";

                    $sql_transacao = "INSERT INTO transacoes_financeiras (tipo, valor, descricao, categoria, referencia_id, tabela_referencia, data_transacao) VALUES ('entrada', ?, ?, ?, ?, 'vendas', NOW())";
                    $stmt_transacao = $conn->prepare($sql_transacao);
                    $stmt_transacao->bind_param("dssi", $valor_total, $descricao_transacao, $categoria_transacao, $venda_id);

                    if (!$stmt_transacao->execute()) {
                        throw new Exception("Erro ao registrar transação financeira: " . $stmt_transacao->error);
                    }
                    $stmt_transacao->close();
                } else {
                    // Já existe transação, atualizar valor
                    $sql_update_transacao = "UPDATE transacoes_financeiras SET valor = ?, data_transacao = NOW() WHERE referencia_id = ? AND tabela_referencia = 'vendas'";
                    $stmt_update = $conn->prepare($sql_update_transacao);
                    $stmt_update->bind_param("di", $valor_total, $venda_id);

                    if (!$stmt_update->execute()) {
                        throw new Exception("Erro ao atualizar transação financeira: " . $stmt_update->error);
                    }
                    $stmt_update->close();
                }
                $stmt_check->close();
            } else {
                // Se a venda não está concluída, remover transação financeira se existir
                $sql_delete_transacao = "DELETE FROM transacoes_financeiras WHERE referencia_id = ? AND tabela_referencia = 'vendas'";
                $stmt_delete = $conn->prepare($sql_delete_transacao);
                $stmt_delete->bind_param("i", $venda_id);
                $stmt_delete->execute();
                $stmt_delete->close();
            }

            $conn->commit(); // Confirma todas as operações se tudo deu certo
            $message = (empty($venda_id_get) ? "Venda registrada com sucesso!" : "Venda atualizada com sucesso!");
            if ($status_venda == 'concluida') {
                $message .= " Entrada financeira registrada automaticamente.";
            }
            $message_type = "success";
            // Redireciona para evitar reenvio do formulário ou para a página de detalhes da venda
            header("location: vendas.php?message=" . urlencode($message) . "&type=" . $message_type);
            exit;

        } catch (Exception $e) {
            $conn->rollback(); // Reverte todas as operações em caso de erro
            $message = "Erro na transação: " . $e->getMessage();
            $message_type = "danger";
        }
    }
}

// Preencher formulário para edição se um ID for passado via GET
$venda_id_get = $_GET["id"] ?? ''; // Para diferenciar nova venda de edição
if (!empty($venda_id_get) && empty($message)) {
    $venda_id = $venda_id_get;
    $title = "Editar Venda";
    $submit_button_text = "Atualizar Venda";

    // Busca os dados da venda principal
    $sql_venda = "SELECT id, cliente_id, valor_total, forma_pagamento, status_venda FROM vendas WHERE id = ?";
    if ($stmt_venda = $conn->prepare($sql_venda)) {
        $stmt_venda->bind_param("i", $venda_id);
        $stmt_venda->execute();
        $result_venda = $stmt_venda->get_result();
        if ($result_venda->num_rows == 1) {
            $row_venda = $result_venda->fetch_assoc();
            $cliente_id = $row_venda['cliente_id'];
            $valor_total = $row_venda['valor_total'];
            $forma_pagamento = $row_venda['forma_pagamento'];
            $status_venda = $row_venda['status_venda'];
        } else {
            $message = "Venda não encontrada.";
            $message_type = "danger";
            $venda_id = ""; // Reset para tratar como nova venda se ID não encontrado
            $title = "Registrar Nova Venda";
            $submit_button_text = "Registrar Venda";
        }
        $stmt_venda->close();
    } else {
        $message = "Erro ao buscar venda para edição: " . $conn->error;
        $message_type = "danger";
    }

    // Busca os itens da venda
    $sql_itens = "SELECT iv.produto_id, p.nome AS produto_nome, iv.quantidade, iv.preco_unitario
                  FROM itens_venda iv
                  JOIN produtos p ON iv.produto_id = p.id
                  WHERE iv.venda_id = ?";
    if ($stmt_itens = $conn->prepare($sql_itens)) {
        $stmt_itens->bind_param("i", $venda_id);
        $stmt_itens->execute();
        $result_itens = $stmt_itens->get_result();
        while ($item = $result_itens->fetch_assoc()) {
            $itens_da_venda[] = [
                'id' => $item['produto_id'],
                'nome' => $item['produto_nome'],
                'quantidade' => $item['quantidade'],
                'preco_unitario' => $item['preco_unitario']
            ];
        }
        $stmt_itens->close();
    } else {
        $message = "Erro ao buscar itens da venda: " . $conn->error;
        $message_type = "danger";
    }
}

// Fecha a conexão com o banco de dados
$conn->close();
// Lógica para carregar orçamento para Venda (se veio de `orcamentos.php` com `from_orcamento_id`)
$from_orcamento_id = $_GET["from_orcamento_id"] ?? '';
if (!empty($from_orcamento_id) && empty($venda_id_get)) { // Só carrega se não for edição de venda existente
    $sql_orcamento_to_sale = "SELECT o.cliente_id, o.status_orcamento, io.produto_id, p.nome AS produto_nome, p.preco_venda, p.quantidade_estoque, io.quantidade, io.preco_unitario
                              FROM orcamentos o
                              JOIN itens_orcamento io ON o.id = io.orcamento_id
                              JOIN produtos p ON io.produto_id = p.id
                              WHERE o.id = ?";
    if ($stmt_orcamento_to_sale = $conn->prepare($sql_orcamento_to_sale)) {
        $stmt_orcamento_to_sale->bind_param("i", $from_orcamento_id);
        $stmt_orcamento_to_sale->execute();
        $result_orcamento_to_sale = $stmt_orcamento_to_sale->get_result();

        if ($result_orcamento_to_sale->num_rows > 0) {
            $itens_da_venda_convert = [];
            while ($row = $result_orcamento_to_sale->fetch_assoc()) {
                if (empty($cliente_id)) { // Pega o cliente apenas uma vez
                    $cliente_id = $row['cliente_id'];
                    $status_venda = 'pendente'; // Começa como pendente
                    if ($row['status_orcamento'] == 'aprovado') {
                         $status_venda = 'concluida'; // Se o orçamento foi aprovado, a venda pode ser concluída
                    }
                }
                $itens_da_venda_convert[] = [
                    'id' => $row['produto_id'],
                    'nome' => $row['produto_nome'],
                    'quantidade' => $row['quantidade'],
                    // Usa o preço unitário do orçamento se for diferente, ou o preço atual do produto
                    'preco_unitario' => $row['preco_unitario'],
                    'estoque_disponivel' => $row['quantidade_estoque'] // Adiciona estoque para validação JS
                ];
            }
            $itens_da_venda = $itens_da_venda_convert; // Popula a lista de itens do formulário
            $message = "Itens carregados do Orçamento #" . $from_orcamento_id . ". Verifique o estoque e as informações antes de registrar a venda.";
            $message_type = "info";

            // Se for carregado de orçamento, muda o título e botão para "Registrar Venda"
            $title = "Registrar Venda (do Orçamento #" . htmlspecialchars($from_orcamento_id) . ")";
            $submit_button_text = "Registrar Venda";
            // Oculta o campo de ID da venda para forçar uma nova inserção
            $venda_id = "";

        } else {
            $message = "Orçamento para conversão não encontrado ou sem itens.";
            $message_type = "warning";
        }
        $stmt_orcamento_to_sale->close();
    } else {
        $message = "Erro ao preparar consulta para conversão de orçamento: " . $conn->error;
        $message_type = "danger";
    }
}
include_once 'includes/header.php';
?>

<h2 class="mb-4"><i class="fas fa-<?php echo ($venda_id ? 'edit' : 'plus-circle'); ?> me-2"></i> <?php echo $title; ?></h2>

<?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card shadow-sm mb-4">
    <div class="card-body">
        <form id="formVenda" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
            <input type="hidden" name="venda_id" value="<?php echo htmlspecialchars($venda_id); ?>">
            <input type="hidden" name="itens_selecionados_json" id="itens_selecionados_json" value='<?php echo json_encode($itens_da_venda); ?>'>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="cliente_id" class="form-label">Cliente <span class="text-danger">*</span></label>
                    <select class="form-select" id="cliente_id" name="cliente_id" required>
                        <option value="">Selecione um cliente...</option>
                        <?php
                        if ($clientes_options->num_rows > 0) {
                            while($cliente = $clientes_options->fetch_assoc()) {
                                $selected = ($cliente['id'] == $cliente_id) ? 'selected' : '';
                                echo '<option value="' . htmlspecialchars($cliente['id']) . '" ' . $selected . '>' . htmlspecialchars($cliente['nome']) . '</option>';
                            }
                        } else {
                            echo '<option value="" disabled>Nenhum cliente cadastrado.</option>';
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="forma_pagamento" class="form-label">Forma de Pagamento <span class="text-danger">*</span></label>
                    <select class="form-select" id="forma_pagamento" name="forma_pagamento" required>
                        <option value="">Selecione...</option>
                        <option value="Pix" <?php echo ($forma_pagamento == 'Pix' ? 'selected' : ''); ?>>Pix</option>
                        <option value="Cartao_Credito" <?php echo ($forma_pagamento == 'Cartao_Credito' ? 'selected' : ''); ?>>Cartão de Crédito</option>
                        <option value="Boleto" <?php echo ($forma_pagamento == 'Boleto' ? 'selected' : ''); ?>>Boleto</option>
                        <option value="Dinheiro" <?php echo ($forma_pagamento == 'Dinheiro' ? 'selected' : ''); ?>>Dinheiro</option>
                        <option value="Outro" <?php echo ($forma_pagamento == 'Outro' ? 'selected' : ''); ?>>Outro</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="status_venda" class="form-label">Status da Venda <span class="text-danger">*</span></label>
                    <select class="form-select" id="status_venda" name="status_venda" required>
                        <option value="pendente" <?php echo ($status_venda == 'pendente' ? 'selected' : ''); ?>>Pendente</option>
                        <option value="concluida" <?php echo ($status_venda == 'concluida' ? 'selected' : ''); ?>>Concluída</option>
                        <option value="cancelada" <?php echo ($status_venda == 'cancelada' ? 'selected' : ''); ?>>Cancelada</option>
                    </select>
                </div>
            </div>

            <hr class="my-4">
            <h4><i class="fas fa-boxes me-2 text-primary"></i> Itens da Venda</h4>

            <div class="row mb-3 align-items-end">
                <div class="col-md-6 mb-3">
                    <label for="produto_select" class="form-label">Adicionar Produto</label>
                    <select class="form-select" id="produto_select">
                        <option value="">Selecione um produto...</option>
                        <?php
                        if ($produtos_options->num_rows > 0) {
                            while($produto = $produtos_options->fetch_assoc()) {
                                echo '<option value="' . htmlspecialchars($produto['id']) . '" data-preco="' . htmlspecialchars($produto['preco_venda']) . '" data-estoque="' . htmlspecialchars($produto['quantidade_estoque']) . '">' . htmlspecialchars($produto['nome']) . ' (Estoque: ' . htmlspecialchars($produto['quantidade_estoque']) . ')</option>';
                            }
                        } else {
                            echo '<option value="" disabled>Nenhum produto cadastrado.</option>';
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label for="quantidade_item" class="form-label">Qtd.</label>
                    <input type="number" class="form-control" id="quantidade_item" value="1" min="1">
                </div>
                <div class="col-md-2 mb-3">
                    <label for="preco_unitario_item" class="form-label">Preço Unit.</label>
                    <input type="text" class="form-control" id="preco_unitario_item" value="0,00" placeholder="0,00">
                </div>
                <div class="col-md-2 mb-3">
                    <button type="button" class="btn btn-info w-100" id="addItemBtn">
                        <i class="fas fa-plus-circle me-1"></i> Adicionar
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="tabelaItens">
                    <thead>
                        <tr>
                            <th>Produto</th>
                            <th>Quantidade</th>
                            <th>Preço Unit.</th>
                            <th>Subtotal</th>
                            <th class="text-center">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="3" class="text-end">Total da Venda:</th>
                            <th id="valor_total_display">R$ 0,00</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <button type="submit" class="btn btn-success btn-lg">
                    <i class="fas fa-<?php echo ($venda_id ? 'save' : 'check'); ?> me-2"></i> <?php echo $submit_button_text; ?>
                </button>
                <a href="vendas.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i> Voltar para Vendas
                </a>
            </div>
        </form>
    </div>
</div>

<?php include_once 'includes/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const produtoSelect = document.getElementById('produto_select');
        const quantidadeInput = document.getElementById('quantidade_item');
        const precoUnitarioInput = document.getElementById('preco_unitario_item');
        const addItemBtn = document.getElementById('addItemBtn');
        const tabelaItensBody = document.querySelector('#tabelaItens tbody');
        const valorTotalDisplay = document.getElementById('valor_total_display');
        const itensSelecionadosJsonInput = document.getElementById('itens_selecionados_json');

        // Array para armazenar os itens da venda (para enviar via JSON)
        let itensDaVenda = JSON.parse(itensSelecionadosJsonInput.value || '[]');

        // Função para formatar números para BRL
        function formatarMoeda(valor) {
            return parseFloat(valor).toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
        }

        // Função para calcular e atualizar o total da venda
        function calcularTotal() {
            let total = 0;
            itensDaVenda.forEach(item => {
                total += item.quantidade * item.preco_unitario;
            });
            valorTotalDisplay.textContent = formatarMoeda(total);
            itensSelecionadosJsonInput.value = JSON.stringify(itensDaVenda); // Atualiza o campo oculto
        }

        // Função para renderizar a tabela de itens
        function renderizarItens() {
            tabelaItensBody.innerHTML = ''; // Limpa a tabela
            itensDaVenda.forEach((item, index) => {
                const subtotal = item.quantidade * item.preco_unitario;
                const row = `
                    <tr>
                        <td>${item.nome}</td>
                        <td>${item.quantidade}</td>
                        <td>${formatarMoeda(item.preco_unitario)}</td>
                        <td>${formatarMoeda(subtotal)}</td>
                        <td class="text-center">
                            <button type="button" class="btn btn-danger btn-sm remover-item-btn" data-index="${index}" title="Remover">
                                <i class="fas fa-times"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tabelaItensBody.insertAdjacentHTML('beforeend', row);
            });
            calcularTotal();
        }

        // Event listener para carregar preço e estoque ao selecionar produto
        produtoSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                precoUnitarioInput.value = parseFloat(selectedOption.dataset.preco).toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                quantidadeInput.value = 1; // Reseta a quantidade para 1
                // Opcional: mostrar estoque disponível em algum lugar na UI
            } else {
                precoUnitarioInput.value = '0,00';
            }
        });

        // Event listener para adicionar item à venda
        addItemBtn.addEventListener('click', function() {
            const selectedOption = produtoSelect.options[produtoSelect.selectedIndex];
            const produtoId = selectedOption.value;
            const produtoNome = selectedOption.textContent.split(' (Estoque:')[0]; // Remove o estoque do nome
            const quantidade = parseInt(quantidadeInput.value);
            // Converte a string de preço para número antes de usar (substitui vírgula por ponto)
            const precoUnitario = parseFloat(precoUnitarioInput.value.replace(',', '.'));
            const estoqueDisponivel = parseInt(selectedOption.dataset.estoque);

            if (!produtoId || isNaN(quantidade) || quantidade <= 0 || isNaN(precoUnitario) || precoUnitario < 0) {
                alert('Por favor, selecione um produto e insira quantidades e preços válidos.');
                return;
            }

            // Verifica estoque antes de adicionar
            if (quantidade > estoqueDisponivel) {
                 alert(`Estoque insuficiente para ${produtoNome}. Disponível: ${estoqueDisponivel}.`);
                 return;
            }

            // Verifica se o produto já está na lista para apenas atualizar a quantidade
            let itemExistente = itensDaVenda.find(item => item.id == produtoId);
            if (itemExistente) {
                // Ao editar, não precisamos verificar o estoque novamente pois ele já foi revertido.
                // Mas ao adicionar um novo item ou aumentar, precisamos.
                // Aqui estamos somando. Em uma aplicação mais complexa, você pode permitir editar a quantidade diretamente na tabela.
                itemExistente.quantidade += quantidade;
                itemExistente.preco_unitario = precoUnitario; // Atualiza o preço caso tenha mudado
            } else {
                itensDaVenda.push({
                    id: produtoId,
                    nome: produtoNome,
                    quantidade: quantidade,
                    preco_unitario: precoUnitario
                });
            }

            renderizarItens();
            // Limpa os campos após adicionar
            produtoSelect.value = '';
            quantidadeInput.value = '1';
            precoUnitarioInput.value = '0,00';
        });

        // Event listener para remover item da venda (delegação de evento)
        tabelaItensBody.addEventListener('click', function(event) {
            if (event.target.classList.contains('remover-item-btn') || event.target.closest('.remover-item-btn')) {
                const button = event.target.classList.contains('remover-item-btn') ? event.target : event.target.closest('.remover-item-btn');
                const indexToRemove = parseInt(button.dataset.index);
                itensDaVenda.splice(indexToRemove, 1); // Remove o item do array
                renderizarItens(); // Redesenha a tabela
            }
        });

        // Event listener para o formulário ser enviado
        document.getElementById('formVenda').addEventListener('submit', function(event) {
            if (itensDaVenda.length === 0) {
                alert('Por favor, adicione pelo menos um produto à venda.');
                event.preventDefault(); // Impede o envio do formulário
            }
            // O campo oculto itens_selecionados_json já é atualizado por renderizarItens() e calcularTotal()
        });

        // Chamar renderizarItens() para carregar os itens existentes na edição
        renderizarItens();
    });
</script>