/* Importar Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Reset completo */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'Poppins', sans-serif;
    background: #f8fafc;
    color: #334155;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Layout principal */
.dashboard-layout {
    display: flex;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    overflow-y: auto;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.sidebar-brand {
    color: #fff;
    font-size: 1.5rem;
    font-weight: 700;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.sidebar-brand:hover {
    color: #fff;
    text-decoration: none;
}

.sidebar-brand i {
    color: #3b82f6;
    font-size: 2rem;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    margin: 0.25rem 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.875rem 1rem;
    color: #94a3b8;
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    text-decoration: none;
}

.nav-link.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #fff;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.nav-link i {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

/* Topbar */
.topbar {
    height: 80px;
    background: #fff;
    border-bottom: 1px solid #e2e8f0;
    position: fixed;
    top: 0;
    left: 280px;
    right: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #64748b;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: #f1f5f9;
    color: #3b82f6;
}

.topbar-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    text-align: right;
}

.user-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.875rem;
}

.user-role {
    font-size: 0.75rem;
    color: #64748b;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-weight: 600;
}

/* Main Content */
.main-content {
    margin-left: 280px;
    margin-top: 80px;
    padding: 2rem;
    min-height: calc(100vh - 80px);
    background: #f8fafc;
    width: calc(100% - 280px);
}

/* Page Header */
.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-title i {
    color: #3b82f6;
}

.page-subtitle {
    color: #64748b;
    font-size: 1rem;
}

/* Stats Cards */
.stats-card {
    background: #fff;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.stats-card.primary::before { background: #3b82f6; }
.stats-card.success::before { background: #10b981; }
.stats-card.warning::before { background: #f59e0b; }
.stats-card.danger::before { background: #ef4444; }
.stats-card.info::before { background: #06b6d4; }

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stats-icon.primary { background: rgba(59, 130, 246, 0.1); color: #3b82f6; }
.stats-icon.success { background: rgba(16, 185, 129, 0.1); color: #10b981; }
.stats-icon.warning { background: rgba(245, 158, 11, 0.1); color: #f59e0b; }
.stats-icon.danger { background: rgba(239, 68, 68, 0.1); color: #ef4444; }
.stats-icon.info { background: rgba(6, 182, 212, 0.1); color: #06b6d4; }

.stats-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e293b;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stats-label {
    color: #64748b;
    font-weight: 500;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-change {
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.5rem;
}

.stats-change.positive { color: #10b981; }
.stats-change.negative { color: #ef4444; }

/* Modern Cards */
.modern-card {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.card-header-modern {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-body-modern {
    padding: 2rem;
}

/* Animações */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Botões */
.btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #fff;
}

.btn-outline-primary {
    border: 2px solid #3b82f6;
    color: #3b82f6;
    background: transparent;
}

.btn-outline-primary:hover {
    background: #3b82f6;
    color: #fff;
}

/* Tabelas */
.table-modern {
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-modern thead th {
    background: #f8fafc;
    border: none;
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-modern tbody td {
    padding: 1.25rem 1.5rem;
    border: none;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.table-modern tbody tr:hover {
    background: #f8fafc;
}

/* Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-success {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.status-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.status-info {
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* Product Specific Styles */
.product-avatar {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.25rem;
}

.mobile-product-card {
    background: #fff;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.mobile-product-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.mobile-info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.mobile-info-item small {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.7rem;
}

/* Form Styles */
.form-control {
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.input-group .form-control {
    border-radius: 12px 0 0 12px;
}

.input-group .btn {
    border-radius: 0 12px 12px 0;
}

/* Alert Styles */
.alert {
    border-radius: 12px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border-left: 4px solid #10b981;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-left: 4px solid #ef4444;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border-left: 4px solid #f59e0b;
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
    border-left: 4px solid #3b82f6;
}

/* Badge Styles */
.badge {
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: 0.75rem;
}

.bg-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
}

.bg-secondary {
    background: #6b7280 !important;
}

/* Code Styles */
code {
    background: #f1f5f9 !important;
    color: #475569 !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 6px !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
}

/* Client Specific Styles */
.client-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.25rem;
    font-weight: 700;
}

.mobile-client-card {
    background: #fff;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.mobile-client-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

/* Sale Specific Styles */
.sale-avatar {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.25rem;
}

.mobile-sale-card {
    background: #fff;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.mobile-sale-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

/* Company Specific Styles */
.company-avatar {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.25rem;
    font-weight: 700;
}

/* User Specific Styles */
.user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.25rem;
    font-weight: 700;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 2rem;
    font-weight: 700;
    box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
}

.mobile-user-card {
    background: #fff;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.mobile-user-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-overlay.show {
    display: block;
    opacity: 1;
}

/* Responsividade */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .topbar {
        left: 0;
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .topbar {
        padding: 0 1rem;
    }

    .main-content {
        padding: 1rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .stats-card {
        padding: 1.5rem;
    }

    .stats-value {
        font-size: 2rem;
    }

    .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

@media (max-width: 576px) {
    .topbar {
        height: 70px;
        padding: 0 0.75rem;
    }

    .main-content {
        margin-top: 70px;
        padding: 0.75rem;
    }

    .page-title {
        font-size: 1.25rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .stats-card {
        padding: 1rem;
    }

    .stats-value {
        font-size: 1.75rem;
    }

    .user-info {
        display: none;
    }
}
