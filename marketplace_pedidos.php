<?php
session_start();

// Verifica se o usuário está logado
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: index.php");
    exit;
}

require_once 'includes/db_connect.php';

$message = '';
$message_type = '';

// Processar ações
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $acao = $_POST['acao'] ?? '';
    
    switch ($acao) {
        case 'atualizar_status':
            $pedido_id = (int)$_POST['pedido_id'];
            $novo_status = $_POST['novo_status'];
            
            $sql_update = "UPDATE marketplace_pedidos SET status_pedido = ? WHERE id = ?";
            $stmt_update = $conn->prepare($sql_update);
            $stmt_update->bind_param("si", $novo_status, $pedido_id);
            
            if ($stmt_update->execute()) {
                // Se confirmado, atualizar data de confirmação
                if ($novo_status == 'confirmado') {
                    $sql_confirm = "UPDATE marketplace_pedidos SET data_confirmacao = NOW() WHERE id = ?";
                    $stmt_confirm = $conn->prepare($sql_confirm);
                    $stmt_confirm->bind_param("i", $pedido_id);
                    $stmt_confirm->execute();
                }
                
                $message = "Status do pedido atualizado com sucesso!";
                $message_type = "success";
            } else {
                $message = "Erro ao atualizar status: " . $conn->error;
                $message_type = "danger";
            }
            break;
    }
}

// Filtros
$filtro_status = $_GET['status'] ?? '';
$filtro_data_inicio = $_GET['data_inicio'] ?? '';
$filtro_data_fim = $_GET['data_fim'] ?? '';

// Buscar pedidos
$sql_pedidos = "SELECT mp.*, c.nome as cliente_nome, c.email as cliente_email,
                       COUNT(mip.id) as total_itens
                FROM marketplace_pedidos mp
                LEFT JOIN clientes c ON mp.cliente_id = c.id
                LEFT JOIN marketplace_itens_pedido mip ON mp.id = mip.pedido_id
                WHERE 1=1";

$params = [];
$types = "";

if (!empty($filtro_status)) {
    $sql_pedidos .= " AND mp.status_pedido = ?";
    $params[] = $filtro_status;
    $types .= "s";
}

if (!empty($filtro_data_inicio)) {
    $sql_pedidos .= " AND DATE(mp.data_pedido) >= ?";
    $params[] = $filtro_data_inicio;
    $types .= "s";
}

if (!empty($filtro_data_fim)) {
    $sql_pedidos .= " AND DATE(mp.data_pedido) <= ?";
    $params[] = $filtro_data_fim;
    $types .= "s";
}

$sql_pedidos .= " GROUP BY mp.id ORDER BY mp.data_pedido DESC";

$stmt_pedidos = $conn->prepare($sql_pedidos);
if (!empty($params)) {
    $stmt_pedidos->bind_param($types, ...$params);
}
$stmt_pedidos->execute();
$result_pedidos = $stmt_pedidos->get_result();

// Estatísticas
$sql_stats = "SELECT 
    COUNT(*) as total_pedidos,
    SUM(CASE WHEN status_pedido = 'pendente' THEN 1 ELSE 0 END) as pedidos_pendentes,
    SUM(CASE WHEN status_pedido = 'confirmado' THEN 1 ELSE 0 END) as pedidos_confirmados,
    SUM(CASE WHEN status_pedido = 'entregue' THEN 1 ELSE 0 END) as pedidos_entregues,
    SUM(CASE WHEN status_pedido = 'entregue' THEN valor_total ELSE 0 END) as valor_entregue
    FROM marketplace_pedidos";
$result_stats = $conn->query($sql_stats);
$stats = $result_stats->fetch_assoc();

include_once 'includes/header.php';
?>

<!-- Page Header -->
<div class="page-header fade-in-up">
    <h1 class="page-title">
        <i class="fas fa-shopping-cart"></i>
        Marketplace - Pedidos
    </h1>
    <p class="page-subtitle">
        Gerencie os pedidos recebidos através do marketplace
    </p>
</div>

<?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show fade-in-up" role="alert">
        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="stats-card primary fade-in-up">
            <div class="stats-icon primary">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stats-value"><?php echo $stats['total_pedidos']; ?></div>
            <div class="stats-label">Total de Pedidos</div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card warning fade-in-up">
            <div class="stats-icon warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-value"><?php echo $stats['pedidos_pendentes']; ?></div>
            <div class="stats-label">Pendentes</div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card info fade-in-up">
            <div class="stats-icon info">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-value"><?php echo $stats['pedidos_confirmados']; ?></div>
            <div class="stats-label">Confirmados</div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card success fade-in-up">
            <div class="stats-icon success">
                <i class="fas fa-truck"></i>
            </div>
            <div class="stats-value"><?php echo $stats['pedidos_entregues']; ?></div>
            <div class="stats-label">Entregues</div>
            <div class="stats-change positive">
                <i class="fas fa-dollar-sign"></i> R$ <?php echo number_format($stats['valor_entregue'], 0, ',', '.'); ?>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="modern-card fade-in-up mb-4">
    <div class="card-header-modern">
        <i class="fas fa-filter"></i>
        Filtros
    </div>
    <div class="card-body-modern">
        <form method="GET" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">Todos os status</option>
                        <option value="pendente" <?php echo ($filtro_status == 'pendente' ? 'selected' : ''); ?>>Pendente</option>
                        <option value="confirmado" <?php echo ($filtro_status == 'confirmado' ? 'selected' : ''); ?>>Confirmado</option>
                        <option value="preparando" <?php echo ($filtro_status == 'preparando' ? 'selected' : ''); ?>>Preparando</option>
                        <option value="entregue" <?php echo ($filtro_status == 'entregue' ? 'selected' : ''); ?>>Entregue</option>
                        <option value="cancelado" <?php echo ($filtro_status == 'cancelado' ? 'selected' : ''); ?>>Cancelado</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="data_inicio" class="form-label">Data Início</label>
                    <input type="date" class="form-control" id="data_inicio" name="data_inicio" value="<?php echo $filtro_data_inicio; ?>">
                </div>
                
                <div class="col-md-3">
                    <label for="data_fim" class="form-label">Data Fim</label>
                    <input type="date" class="form-control" id="data_fim" name="data_fim" value="<?php echo $filtro_data_fim; ?>">
                </div>
                
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>Filtrar
                    </button>
                    <a href="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Lista de Pedidos -->
<div class="modern-card fade-in-up">
    <div class="card-header-modern">
        <i class="fas fa-list"></i>
        Pedidos do Marketplace
        <div class="ms-auto">
            <span class="badge bg-primary"><?php echo $result_pedidos->num_rows; ?> pedidos</span>
        </div>
    </div>
    <div class="card-body-modern">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Pedido</th>
                        <th>Cliente</th>
                        <th>Data</th>
                        <th>Valor</th>
                        <th>Faturamento</th>
                        <th>Status</th>
                        <th>Itens</th>
                        <th class="text-center">Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($result_pedidos && $result_pedidos->num_rows > 0) {
                        while($pedido = $result_pedidos->fetch_assoc()) {
                            // Define a classe de badge para o status
                            $status_class = '';
                            switch ($pedido['status_pedido']) {
                                case 'confirmado':
                                case 'preparando':
                                    $status_class = 'bg-info';
                                    break;
                                case 'pendente':
                                    $status_class = 'bg-warning text-dark';
                                    break;
                                case 'entregue':
                                    $status_class = 'bg-success';
                                    break;
                                case 'cancelado':
                                    $status_class = 'bg-danger';
                                    break;
                                default:
                                    $status_class = 'bg-secondary';
                            }
                            
                            $tipo_faturamento_texto = '';
                            switch ($pedido['tipo_faturamento']) {
                                case 'avista': $tipo_faturamento_texto = 'À Vista'; break;
                                case '15_dias': $tipo_faturamento_texto = '15 dias'; break;
                                case '20_dias': $tipo_faturamento_texto = '20 dias'; break;
                                case '30_dias': $tipo_faturamento_texto = '30 dias'; break;
                            }
                            ?>
                            <tr>
                                <td>
                                    <strong>#<?php echo htmlspecialchars($pedido['numero_pedido']); ?></strong><br>
                                    <small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($pedido['data_pedido'])); ?></small>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($pedido['cliente_nome']); ?></strong><br>
                                    <small class="text-muted"><?php echo htmlspecialchars($pedido['cliente_email']); ?></small>
                                </td>
                                <td>
                                    <?php echo date('d/m/Y', strtotime($pedido['data_pedido'])); ?><br>
                                    <?php if ($pedido['data_entrega_agendada']): ?>
                                        <small class="text-muted">
                                            <i class="fas fa-truck me-1"></i>
                                            <?php echo date('d/m/Y', strtotime($pedido['data_entrega_agendada'])); ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong>R$ <?php echo number_format($pedido['valor_total'], 2, ',', '.'); ?></strong>
                                </td>
                                <td>
                                    <?php echo $tipo_faturamento_texto; ?><br>
                                    <?php if ($pedido['data_vencimento']): ?>
                                        <small class="text-muted">
                                            Venc: <?php echo date('d/m/Y', strtotime($pedido['data_vencimento'])); ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge <?php echo $status_class; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $pedido['status_pedido'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        <?php echo $pedido['total_itens']; ?> itens
                                    </span>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="marketplace_detalhes_pedido.php?id=<?php echo $pedido['id']; ?>" class="btn btn-info btn-sm" title="Ver Detalhes">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        <?php if ($pedido['status_pedido'] != 'cancelado' && $pedido['status_pedido'] != 'entregue'): ?>
                                            <div class="dropdown">
                                                <button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <?php if ($pedido['status_pedido'] == 'pendente'): ?>
                                                        <li>
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="acao" value="atualizar_status">
                                                                <input type="hidden" name="pedido_id" value="<?php echo $pedido['id']; ?>">
                                                                <input type="hidden" name="novo_status" value="confirmado">
                                                                <button type="submit" class="dropdown-item">
                                                                    <i class="fas fa-check me-2"></i>Confirmar
                                                                </button>
                                                            </form>
                                                        </li>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($pedido['status_pedido'] == 'confirmado'): ?>
                                                        <li>
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="acao" value="atualizar_status">
                                                                <input type="hidden" name="pedido_id" value="<?php echo $pedido['id']; ?>">
                                                                <input type="hidden" name="novo_status" value="preparando">
                                                                <button type="submit" class="dropdown-item">
                                                                    <i class="fas fa-cog me-2"></i>Preparando
                                                                </button>
                                                            </form>
                                                        </li>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($pedido['status_pedido'] == 'preparando'): ?>
                                                        <li>
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="acao" value="atualizar_status">
                                                                <input type="hidden" name="pedido_id" value="<?php echo $pedido['id']; ?>">
                                                                <input type="hidden" name="novo_status" value="entregue">
                                                                <button type="submit" class="dropdown-item">
                                                                    <i class="fas fa-truck me-2"></i>Marcar como Entregue
                                                                </button>
                                                            </form>
                                                        </li>
                                                    <?php endif; ?>
                                                    
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="acao" value="atualizar_status">
                                                            <input type="hidden" name="pedido_id" value="<?php echo $pedido['id']; ?>">
                                                            <input type="hidden" name="novo_status" value="cancelado">
                                                            <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Tem certeza que deseja cancelar este pedido?')">
                                                                <i class="fas fa-times me-2"></i>Cancelar
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php
                        }
                    } else {
                        echo '<tr><td colspan="8" class="text-center">Nenhum pedido encontrado.</td></tr>';
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
$conn->close();
include_once 'includes/footer.php';
?>
