<?php
session_start();

// Verifica se o usuário está logado
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: index.php");
    exit;
}

require_once 'includes/db_connect.php';

$message = '';
$message_type = '';

// --- Lógica para Exclusão de Venda ---
// A exclusão de uma venda é complexa pois envolve itens_venda e, possivelmente, reversão de estoque.
// Por simplicidade inicial, vamos desabilitar a exclusão direta por ID e focar no status 'cancelada'.
// Se realmente precisar excluir, considere um sistema de "soft delete" ou uma lógica que reverta o estoque.
/*
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $venda_id_to_delete = $_GET['id'];

    // Para exclusão real, primeiro delete os itens da venda, depois a venda em si.
    // E considere reverter a quantidade do estoque dos produtos vendidos.
    // Exemplo simplificado de exclusão (NÃO REVERTE ESTOQUE!)
    $conn->begin_transaction(); // Inicia transação
    try {
        // Excluir itens da venda
        $sql_delete_itens = "DELETE FROM itens_venda WHERE venda_id = ?";
        $stmt_delete_itens = $conn->prepare($sql_delete_itens);
        $stmt_delete_itens->bind_param("i", $venda_id_to_delete);
        $stmt_delete_itens->execute();
        $stmt_delete_itens->close();

        // Excluir a venda
        $sql_delete_venda = "DELETE FROM vendas WHERE id = ?";
        $stmt_delete_venda = $conn->prepare($sql_delete_venda);
        $stmt_delete_venda->bind_param("i", $venda_id_to_delete);
        $stmt_delete_venda->execute();
        $stmt_delete_venda->close();

        $conn->commit(); // Confirma transação
        $message = "Venda e seus itens excluídos com sucesso! (Estoque NÃO revertido automaticamente)";
        $message_type = "success";

    } catch (mysqli_sql_exception $e) {
        $conn->rollback(); // Reverte transação em caso de erro
        $message = "Erro ao excluir a venda: " . $e->getMessage();
        $message_type = "danger";
    }
}
*/

// --- Lógica para buscar todas as vendas ---
$sql_select_vendas = "SELECT v.id, c.nome AS nome_cliente, v.data_venda, v.valor_total, v.forma_pagamento, v.status_venda
                      FROM vendas v
                      LEFT JOIN clientes c ON v.cliente_id = c.id
                      ORDER BY v.data_venda DESC";
$result_vendas = $conn->query($sql_select_vendas);

// Estatísticas das vendas
$total_vendas = $result_vendas->num_rows;
$vendas_concluidas = 0;
$vendas_pendentes = 0;
$vendas_canceladas = 0;
$valor_total_vendas = 0;
$valor_vendas_mes = 0;

$result_vendas->data_seek(0);
while($row = $result_vendas->fetch_assoc()) {
    switch($row['status_venda']) {
        case 'concluida':
            $vendas_concluidas++;
            $valor_total_vendas += $row['valor_total'];
            break;
        case 'pendente':
            $vendas_pendentes++;
            break;
        case 'cancelada':
            $vendas_canceladas++;
            break;
    }

    // Vendas do mês atual
    if (date('Y-m', strtotime($row['data_venda'])) == date('Y-m')) {
        $valor_vendas_mes += $row['valor_total'];
    }
}

$conn->close();

include_once 'includes/header.php';
?>

<!-- Page Header -->
<div class="page-header fade-in-up">
    <h1 class="page-title">
        <i class="fas fa-shopping-cart"></i>
        Gerenciamento de Vendas
    </h1>
    <p class="page-subtitle">Controle todas as suas vendas</p>
</div>

<?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show fade-in-up" role="alert">
        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-6 col-lg-3">
        <div class="stats-card primary fade-in-up">
            <div class="stats-icon primary">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stats-value"><?php echo $total_vendas; ?></div>
            <div class="stats-label">Total de Vendas</div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card success fade-in-up">
            <div class="stats-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-value"><?php echo $vendas_concluidas; ?></div>
            <div class="stats-label">Concluídas</div>
            <div class="stats-change positive">
                <i class="fas fa-arrow-up"></i> R$ <?php echo number_format($valor_total_vendas, 0, ',', '.'); ?>
            </div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card warning fade-in-up">
            <div class="stats-icon warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-value"><?php echo $vendas_pendentes; ?></div>
            <div class="stats-label">Pendentes</div>
            <?php if ($vendas_pendentes > 0): ?>
                <div class="stats-change negative">
                    <i class="fas fa-exclamation-triangle"></i> Atenção necessária
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card info fade-in-up">
            <div class="stats-icon info">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stats-value">R$ <?php echo number_format($valor_vendas_mes / 1000, 0); ?>K</div>
            <div class="stats-label">Vendas do Mês</div>
        </div>
    </div>
</div>

<!-- Action Bar -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="modern-card fade-in-up">
            <div class="card-body-modern">
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-between align-items-md-center">
                    <div class="d-flex flex-column flex-sm-row gap-2">
                        <a href="registrar_venda.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i> Nova Venda
                        </a>
                        <button class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i> Imprimir
                        </button>
                        <button class="btn btn-outline-primary" onclick="exportSales()">
                            <i class="fas fa-download me-2"></i> Exportar
                        </button>
                    </div>
                    <div class="d-flex gap-2 flex-grow-1 flex-md-grow-0">
                        <div class="input-group" style="max-width: 300px;">
                            <input type="text" class="form-control" placeholder="Buscar vendas..." id="searchInput">
                            <button class="btn btn-outline-primary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales Table -->
<div class="modern-card fade-in-up">
    <div class="card-header-modern">
        <i class="fas fa-list"></i>
        Lista de Vendas
        <div class="ms-auto">
            <span class="badge bg-primary"><?php echo $total_vendas; ?> vendas</span>
        </div>
    </div>
    <div class="card-body-modern">
        <?php if ($result_vendas->num_rows > 0): ?>
            <!-- Desktop Table -->
            <div class="table-modern d-none d-md-block">
                <table class="table table-hover mb-0" id="vendasTable">
                    <thead>
                        <tr>
                            <th>Venda</th>
                            <th>Cliente</th>
                            <th>Data</th>
                            <th>Valor</th>
                            <th>Pagamento</th>
                            <th>Status</th>
                            <th class="text-center">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $result_vendas->data_seek(0);
                        while($row = $result_vendas->fetch_assoc()) {
                            // Define a classe de badge para o status
                            $status_class = '';
                            switch ($row['status_venda']) {
                                case 'concluida':
                                    $status_class = 'status-success';
                                    break;
                                case 'pendente':
                                    $status_class = 'status-warning';
                                    break;
                                case 'cancelada':
                                    $status_class = 'status-danger';
                                    break;
                                default:
                                    $status_class = 'status-info';
                            }
                            ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="sale-avatar me-3">
                                            <i class="fas fa-receipt"></i>
                                        </div>
                                        <div>
                                            <div class="fw-semibold">#<?php echo $row['id']; ?></div>
                                            <small class="text-muted">Venda</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="client-avatar me-2" style="width: 32px; height: 32px; font-size: 0.75rem;">
                                            <?php echo strtoupper(substr($row['nome_cliente'] ?? 'C', 0, 1)); ?>
                                        </div>
                                        <?php echo htmlspecialchars($row['nome_cliente'] ?? 'Cliente não informado'); ?>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-semibold"><?php echo date('d/m/Y', strtotime($row['data_venda'])); ?></div>
                                        <small class="text-muted"><?php echo date('H:i', strtotime($row['data_venda'])); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">R$ <?php echo number_format($row['valor_total'], 2, ',', '.'); ?></span>
                                </td>
                                <td>
                                    <span class="text-muted"><?php echo htmlspecialchars($row['forma_pagamento']); ?></span>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $status_class; ?>">
                                        <?php echo htmlspecialchars(ucfirst($row['status_venda'])); ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="detalhes_venda.php?id=<?php echo $row['id']; ?>"
                                           class="btn btn-outline-primary btn-sm" title="Ver Detalhes">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="registrar_venda.php?id=<?php echo $row['id']; ?>"
                                           class="btn btn-outline-primary btn-sm" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-outline-warning btn-sm"
                                                onclick="changeStatus(<?php echo $row['id']; ?>)" title="Mudar Status">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>

            <!-- Mobile Cards -->
            <div class="d-block d-md-none">
                <?php
                $result_vendas->data_seek(0);
                while($row = $result_vendas->fetch_assoc()) {
                    $status_class = '';
                    switch ($row['status_venda']) {
                        case 'concluida':
                            $status_class = 'status-success';
                            break;
                        case 'pendente':
                            $status_class = 'status-warning';
                            break;
                        case 'cancelada':
                            $status_class = 'status-danger';
                            break;
                        default:
                            $status_class = 'status-info';
                    }
                    ?>
                    <div class="mobile-sale-card mb-3">
                        <div class="d-flex align-items-start mb-3">
                            <div class="sale-avatar me-3">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Venda #<?php echo $row['id']; ?></h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted"><?php echo htmlspecialchars($row['nome_cliente'] ?? 'Cliente não informado'); ?></small>
                                    <span class="status-badge <?php echo $status_class; ?>">
                                        <?php echo htmlspecialchars(ucfirst($row['status_venda'])); ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="row g-3 mb-3">
                            <div class="col-6">
                                <div class="mobile-info-item">
                                    <small class="text-muted">Valor Total</small>
                                    <div class="fw-bold text-success">R$ <?php echo number_format($row['valor_total'], 2, ',', '.'); ?></div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mobile-info-item">
                                    <small class="text-muted">Data</small>
                                    <div><?php echo date('d/m/Y H:i', strtotime($row['data_venda'])); ?></div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mobile-info-item">
                                    <small class="text-muted">Forma de Pagamento</small>
                                    <div><?php echo htmlspecialchars($row['forma_pagamento']); ?></div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <a href="detalhes_venda.php?id=<?php echo $row['id']; ?>"
                               class="btn btn-outline-primary btn-sm flex-fill">
                                <i class="fas fa-eye me-1"></i> Detalhes
                            </a>
                            <a href="registrar_venda.php?id=<?php echo $row['id']; ?>"
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button class="btn btn-outline-warning btn-sm"
                                    onclick="changeStatus(<?php echo $row['id']; ?>)">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                <?php } ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="stats-icon primary mx-auto mb-3">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h5 class="text-muted mb-2">Nenhuma venda registrada</h5>
                <p class="text-muted">Comece registrando sua primeira venda no sistema.</p>
                <a href="registrar_venda.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Registrar Primeira Venda
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function changeStatus(id) {
    const statuses = ['pendente', 'concluida', 'cancelada'];
    const statusNames = ['Pendente', 'Concluída', 'Cancelada'];

    let options = '';
    statuses.forEach((status, index) => {
        options += `<option value="${status}">${statusNames[index]}</option>`;
    });

    const newStatus = prompt(`Escolha o novo status para a venda #${id}:\n\n1 - Pendente\n2 - Concluída\n3 - Cancelada\n\nDigite o número:`);

    if (newStatus && newStatus >= 1 && newStatus <= 3) {
        const selectedStatus = statuses[newStatus - 1];

        if (confirm(`Confirma a alteração do status para "${statusNames[newStatus - 1]}"?`)) {
            // Aqui você pode implementar a lógica para alterar o status
            // Por enquanto, vamos apenas mostrar uma mensagem
            alert(`Status alterado para "${statusNames[newStatus - 1]}" com sucesso!\n\nNota: Esta funcionalidade precisa ser implementada no backend.`);

            // Recarregar a página para mostrar as mudanças
            // window.location.reload();
        }
    }
}

function searchSales() {
    const input = document.getElementById('searchInput');
    const filter = input.value.toLowerCase();

    // Search in desktop table
    const table = document.getElementById('vendasTable');
    if (table) {
        const rows = table.getElementsByTagName('tr');
        for (let i = 1; i < rows.length; i++) {
            const cells = rows[i].getElementsByTagName('td');
            let found = false;

            for (let j = 0; j < cells.length - 1; j++) {
                if (cells[j].textContent.toLowerCase().includes(filter)) {
                    found = true;
                    break;
                }
            }

            rows[i].style.display = found ? '' : 'none';
        }
    }

    // Search in mobile cards
    const mobileCards = document.querySelectorAll('.mobile-sale-card');
    mobileCards.forEach(card => {
        const text = card.textContent.toLowerCase();
        card.style.display = text.includes(filter) ? '' : 'none';
    });
}

function exportSales() {
    // Simple CSV export
    const table = document.getElementById('vendasTable');
    if (!table) return;

    let csv = 'ID,Cliente,Data,Valor,Pagamento,Status\n';

    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        if (row.style.display !== 'none') {
            const cells = row.querySelectorAll('td');
            const data = [
                cells[0].querySelector('.fw-semibold').textContent.trim(),
                cells[1].textContent.trim().split('\n')[0],
                cells[2].querySelector('.fw-semibold').textContent.trim() + ' ' + cells[2].querySelector('small').textContent.trim(),
                cells[3].textContent.trim(),
                cells[4].textContent.trim(),
                cells[5].textContent.trim()
            ];
            csv += data.map(field => `"${field}"`).join(',') + '\n';
        }
    });

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'vendas.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}

// Real-time search
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', searchSales);

        // Clear search on escape
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                this.value = '';
                searchSales();
            }
        });
    }
});
</script>

<?php include_once 'includes/footer.php'; ?>