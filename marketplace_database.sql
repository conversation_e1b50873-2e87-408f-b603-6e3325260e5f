-- ========================================
-- ESTRUTURA DO BANCO PARA E-COMMERCE/MARKETPLACE
-- ========================================

-- 1. Tabela para links exclusivos de clientes
CREATE TABLE `marketplace_links` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cliente_id` int(11) NOT NULL,
  `token_acesso` varchar(64) NOT NULL UNIQUE,
  `ativo` tinyint(1) DEFAULT 1,
  `data_criacao` datetime DEFAULT current_timestamp(),
  `data_expiracao` datetime DEFAULT NULL,
  `ultimo_acesso` datetime DEFAULT NULL,
  `total_acessos` int(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`cliente_id`) REFERENCES `clientes` (`id`) ON DELETE CASCADE,
  INDEX `idx_token` (`token_acesso`),
  INDEX `idx_cliente` (`cliente_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Tabela para carrinho temporário do marketplace
CREATE TABLE `marketplace_carrinho` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token_acesso` varchar(64) NOT NULL,
  `produto_id` int(11) NOT NULL,
  `quantidade` int(11) NOT NULL,
  `preco_unitario` decimal(10,2) NOT NULL,
  `data_adicao` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  FOREIGN KEY (`produto_id`) REFERENCES `produtos` (`id`) ON DELETE CASCADE,
  INDEX `idx_token_carrinho` (`token_acesso`),
  INDEX `idx_produto_carrinho` (`produto_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Tabela para pedidos do marketplace
CREATE TABLE `marketplace_pedidos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cliente_id` int(11) NOT NULL,
  `token_acesso` varchar(64) NOT NULL,
  `numero_pedido` varchar(20) NOT NULL UNIQUE,
  `valor_total` decimal(10,2) NOT NULL,
  `status_pedido` enum('pendente','confirmado','preparando','entregue','cancelado') DEFAULT 'pendente',
  `tipo_faturamento` enum('avista','15_dias','20_dias','30_dias') DEFAULT 'avista',
  `data_vencimento` date DEFAULT NULL,
  `data_entrega_agendada` datetime DEFAULT NULL,
  `endereco_entrega` text DEFAULT NULL,
  `observacoes` text DEFAULT NULL,
  `data_pedido` datetime DEFAULT current_timestamp(),
  `data_confirmacao` datetime DEFAULT NULL,
  `venda_id` int(11) DEFAULT NULL,
  `transacao_financeira_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`cliente_id`) REFERENCES `clientes` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`venda_id`) REFERENCES `vendas` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`transacao_financeira_id`) REFERENCES `transacoes_financeiras` (`id`) ON DELETE SET NULL,
  INDEX `idx_cliente_pedido` (`cliente_id`),
  INDEX `idx_token_pedido` (`token_acesso`),
  INDEX `idx_numero_pedido` (`numero_pedido`),
  INDEX `idx_status` (`status_pedido`),
  INDEX `idx_venda` (`venda_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Tabela para itens dos pedidos do marketplace
CREATE TABLE `marketplace_itens_pedido` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pedido_id` int(11) NOT NULL,
  `produto_id` int(11) NOT NULL,
  `quantidade` int(11) NOT NULL,
  `preco_unitario` decimal(10,2) NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`pedido_id`) REFERENCES `marketplace_pedidos` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`produto_id`) REFERENCES `produtos` (`id`) ON DELETE CASCADE,
  INDEX `idx_pedido_item` (`pedido_id`),
  INDEX `idx_produto_item` (`produto_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Tabela para configurações do marketplace
CREATE TABLE `marketplace_configuracoes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chave` varchar(100) NOT NULL UNIQUE,
  `valor` text DEFAULT NULL,
  `descricao` varchar(255) DEFAULT NULL,
  `data_atualizacao` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  INDEX `idx_chave` (`chave`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. Inserir configurações padrão
INSERT INTO `marketplace_configuracoes` (`chave`, `valor`, `descricao`) VALUES
('marketplace_ativo', '1', 'Marketplace ativo (1) ou inativo (0)'),
('titulo_marketplace', 'Karla Wollinger - Marketplace', 'Título do marketplace'),
('descricao_marketplace', 'Faça seus pedidos online de forma rápida e prática', 'Descrição do marketplace'),
('email_notificacoes', '<EMAIL>', 'Email para receber notificações de pedidos'),
('prazo_entrega_padrao', '3', 'Prazo padrão de entrega em dias úteis'),
('valor_minimo_pedido', '0.00', 'Valor mínimo para pedidos'),
('permitir_agendamento', '1', 'Permitir agendamento de entrega (1) ou não (0)'),
('horario_funcionamento', '08:00-18:00', 'Horário de funcionamento para entregas'),
('dias_funcionamento', '1,2,3,4,5', 'Dias da semana que funciona (1=segunda, 7=domingo)');

-- 7. Adicionar campos extras na tabela produtos para marketplace
ALTER TABLE `produtos` 
ADD COLUMN `ativo_marketplace` tinyint(1) DEFAULT 1,
ADD COLUMN `destaque_marketplace` tinyint(1) DEFAULT 0,
ADD COLUMN `ordem_exibicao` int(11) DEFAULT 0,
ADD COLUMN `imagem_url` varchar(255) DEFAULT NULL,
ADD COLUMN `descricao_completa` text DEFAULT NULL;

-- 8. Adicionar índices para melhor performance
ALTER TABLE `produtos` ADD INDEX `idx_ativo_marketplace` (`ativo_marketplace`);
ALTER TABLE `produtos` ADD INDEX `idx_destaque` (`destaque_marketplace`);
ALTER TABLE `produtos` ADD INDEX `idx_empresa_ativo` (`empresa_id`, `ativo_marketplace`);

-- 9. Trigger para gerar número do pedido automaticamente
DELIMITER $$
CREATE TRIGGER `gerar_numero_pedido` 
BEFORE INSERT ON `marketplace_pedidos`
FOR EACH ROW 
BEGIN
    DECLARE novo_numero VARCHAR(20);
    DECLARE contador INT;
    
    -- Buscar o próximo número sequencial
    SELECT COALESCE(MAX(CAST(SUBSTRING(numero_pedido, 4) AS UNSIGNED)), 0) + 1 
    INTO contador 
    FROM marketplace_pedidos 
    WHERE numero_pedido LIKE CONCAT(DATE_FORMAT(NOW(), '%y%m'), '%');
    
    -- Gerar número no formato YYMM0001
    SET novo_numero = CONCAT(DATE_FORMAT(NOW(), '%y%m'), LPAD(contador, 4, '0'));
    SET NEW.numero_pedido = novo_numero;
    
    -- Calcular data de vencimento baseada no tipo de faturamento
    IF NEW.tipo_faturamento = '15_dias' THEN
        SET NEW.data_vencimento = DATE_ADD(CURDATE(), INTERVAL 15 DAY);
    ELSEIF NEW.tipo_faturamento = '20_dias' THEN
        SET NEW.data_vencimento = DATE_ADD(CURDATE(), INTERVAL 20 DAY);
    ELSEIF NEW.tipo_faturamento = '30_dias' THEN
        SET NEW.data_vencimento = DATE_ADD(CURDATE(), INTERVAL 30 DAY);
    ELSE
        SET NEW.data_vencimento = CURDATE();
    END IF;
END$$
DELIMITER ;

-- 10. View para relatórios do marketplace
CREATE VIEW `vw_marketplace_vendas` AS
SELECT 
    mp.id,
    mp.numero_pedido,
    c.nome as cliente_nome,
    c.email as cliente_email,
    mp.valor_total,
    mp.status_pedido,
    mp.tipo_faturamento,
    mp.data_pedido,
    mp.data_entrega_agendada,
    mp.data_vencimento,
    COUNT(mip.id) as total_itens
FROM marketplace_pedidos mp
LEFT JOIN clientes c ON mp.cliente_id = c.id
LEFT JOIN marketplace_itens_pedido mip ON mp.id = mip.pedido_id
GROUP BY mp.id;
