-- ========================================
-- SCRIPT PARA ATUALIZAR MARKETPLACE COM INTEGRAÇÃO
-- Execute este script para adicionar os campos de integração
-- ========================================

-- 1. Adicionar campos de integração na tabela marketplace_pedidos
ALTER TABLE `marketplace_pedidos` 
ADD COLUMN `venda_id` int(11) DEFAULT NULL AFTER `data_confirmacao`,
ADD COLUMN `transacao_financeira_id` int(11) DEFAULT NULL AFTER `venda_id`;

-- 2. Adicionar foreign keys para integração
ALTER TABLE `marketplace_pedidos` 
ADD FOREIGN KEY (`venda_id`) REFERENCES `vendas` (`id`) ON DELETE SET NULL,
ADD FOREIGN KEY (`transacao_financeira_id`) REFERENCES `transacoes_financeiras` (`id`) ON DELETE SET NULL;

-- 3. Adicionar índices para melhor performance
ALTER TABLE `marketplace_pedidos` 
ADD INDEX `idx_venda` (`venda_id`),
ADD INDEX `idx_transacao` (`transacao_financeira_id`);

-- 4. Adicionar campos extras na tabela produtos para marketplace (se não existirem)
ALTER TABLE `produtos` 
ADD COLUMN `ativo_marketplace` tinyint(1) DEFAULT 1 AFTER `quantidade_estoque`,
ADD COLUMN `destaque_marketplace` tinyint(1) DEFAULT 0 AFTER `ativo_marketplace`,
ADD COLUMN `ordem_exibicao` int(11) DEFAULT 0 AFTER `destaque_marketplace`,
ADD COLUMN `imagem_url` varchar(255) DEFAULT NULL AFTER `ordem_exibicao`,
ADD COLUMN `descricao_completa` text DEFAULT NULL AFTER `imagem_url`;

-- 5. Adicionar índices na tabela produtos
ALTER TABLE `produtos` 
ADD INDEX `idx_ativo_marketplace` (`ativo_marketplace`),
ADD INDEX `idx_destaque` (`destaque_marketplace`),
ADD INDEX `idx_empresa_ativo` (`empresa_id`, `ativo_marketplace`);

-- 6. Inserir configurações do marketplace (se não existirem)
INSERT IGNORE INTO `marketplace_configuracoes` (`chave`, `valor`, `descricao`) VALUES
('marketplace_ativo', '1', 'Marketplace ativo (1) ou inativo (0)'),
('titulo_marketplace', 'Karla Wollinger - Marketplace', 'Título do marketplace'),
('descricao_marketplace', 'Faça seus pedidos online de forma rápida e prática', 'Descrição do marketplace'),
('email_notificacoes', '<EMAIL>', 'Email para receber notificações de pedidos'),
('prazo_entrega_padrao', '3', 'Prazo padrão de entrega em dias úteis'),
('valor_minimo_pedido', '0.00', 'Valor mínimo para pedidos'),
('permitir_agendamento', '1', 'Permitir agendamento de entrega (1) ou não (0)'),
('horario_funcionamento', '08:00-18:00', 'Horário de funcionamento para entregas'),
('dias_funcionamento', '1,2,3,4,5', 'Dias da semana que funciona (1=segunda, 7=domingo)');

-- 7. Atualizar trigger para gerar número do pedido automaticamente
DROP TRIGGER IF EXISTS `gerar_numero_pedido`;

DELIMITER $$
CREATE TRIGGER `gerar_numero_pedido` 
BEFORE INSERT ON `marketplace_pedidos`
FOR EACH ROW 
BEGIN
    DECLARE novo_numero VARCHAR(20);
    DECLARE contador INT;
    
    -- Buscar o próximo número sequencial
    SELECT COALESCE(MAX(CAST(SUBSTRING(numero_pedido, 4) AS UNSIGNED)), 0) + 1 
    INTO contador 
    FROM marketplace_pedidos 
    WHERE numero_pedido LIKE CONCAT(DATE_FORMAT(NOW(), '%y%m'), '%');
    
    -- Gerar número no formato YYMM0001
    SET novo_numero = CONCAT(DATE_FORMAT(NOW(), '%y%m'), LPAD(contador, 4, '0'));
    SET NEW.numero_pedido = novo_numero;
    
    -- Calcular data de vencimento baseada no tipo de faturamento
    IF NEW.tipo_faturamento = '15_dias' THEN
        SET NEW.data_vencimento = DATE_ADD(CURDATE(), INTERVAL 15 DAY);
    ELSEIF NEW.tipo_faturamento = '20_dias' THEN
        SET NEW.data_vencimento = DATE_ADD(CURDATE(), INTERVAL 20 DAY);
    ELSEIF NEW.tipo_faturamento = '30_dias' THEN
        SET NEW.data_vencimento = DATE_ADD(CURDATE(), INTERVAL 30 DAY);
    ELSE
        SET NEW.data_vencimento = CURDATE();
    END IF;
END$$
DELIMITER ;

-- 8. Criar view para relatórios do marketplace (atualizada)
DROP VIEW IF EXISTS `vw_marketplace_vendas`;

CREATE VIEW `vw_marketplace_vendas` AS
SELECT 
    mp.id,
    mp.numero_pedido,
    c.nome as cliente_nome,
    c.email as cliente_email,
    mp.valor_total,
    mp.status_pedido,
    mp.tipo_faturamento,
    mp.data_pedido,
    mp.data_entrega_agendada,
    mp.data_vencimento,
    mp.venda_id,
    mp.transacao_financeira_id,
    v.data_venda,
    tf.data_transacao,
    COUNT(mip.id) as total_itens,
    CASE 
        WHEN mp.venda_id IS NOT NULL AND mp.transacao_financeira_id IS NOT NULL THEN 'Integrado'
        WHEN mp.status_pedido = 'confirmado' THEN 'Pendente'
        ELSE 'Aguardando'
    END as status_integracao
FROM marketplace_pedidos mp
LEFT JOIN clientes c ON mp.cliente_id = c.id
LEFT JOIN marketplace_itens_pedido mip ON mp.id = mip.pedido_id
LEFT JOIN vendas v ON mp.venda_id = v.id
LEFT JOIN transacoes_financeiras tf ON mp.transacao_financeira_id = tf.id
GROUP BY mp.id;

-- 9. Ativar todos os produtos existentes no marketplace por padrão
UPDATE `produtos` SET 
    `ativo_marketplace` = 1,
    `destaque_marketplace` = 0,
    `ordem_exibicao` = 0
WHERE `ativo_marketplace` IS NULL;

-- 10. Criar função para integrar pedidos existentes (opcional)
-- Esta função pode ser usada para integrar pedidos que já foram confirmados mas não integrados

DELIMITER $$
CREATE PROCEDURE `IntegrarPedidosConfirmados`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE pedido_id INT;
    DECLARE cliente_id INT;
    DECLARE valor_total DECIMAL(10,2);
    DECLARE tipo_faturamento VARCHAR(20);
    DECLARE data_vencimento DATE;
    DECLARE numero_pedido VARCHAR(20);
    
    -- Cursor para pedidos confirmados sem integração
    DECLARE cur CURSOR FOR 
        SELECT id, cliente_id, valor_total, tipo_faturamento, data_vencimento, numero_pedido
        FROM marketplace_pedidos 
        WHERE status_pedido = 'confirmado' 
        AND venda_id IS NULL 
        AND transacao_financeira_id IS NULL;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO pedido_id, cliente_id, valor_total, tipo_faturamento, data_vencimento, numero_pedido;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- Aqui você pode adicionar a lógica de integração se necessário
        -- Por enquanto, apenas marca como processado
        SELECT CONCAT('Pedido ', numero_pedido, ' pronto para integração manual') as status;
        
    END LOOP;
    
    CLOSE cur;
END$$
DELIMITER ;

-- 11. Verificar integridade dos dados
SELECT 
    'marketplace_pedidos' as tabela,
    COUNT(*) as total_registros,
    SUM(CASE WHEN venda_id IS NOT NULL THEN 1 ELSE 0 END) as com_venda_integrada,
    SUM(CASE WHEN transacao_financeira_id IS NOT NULL THEN 1 ELSE 0 END) as com_transacao_integrada
FROM marketplace_pedidos

UNION ALL

SELECT 
    'produtos' as tabela,
    COUNT(*) as total_registros,
    SUM(CASE WHEN ativo_marketplace = 1 THEN 1 ELSE 0 END) as ativos_marketplace,
    SUM(CASE WHEN destaque_marketplace = 1 THEN 1 ELSE 0 END) as em_destaque
FROM produtos

UNION ALL

SELECT 
    'marketplace_configuracoes' as tabela,
    COUNT(*) as total_registros,
    0 as campo2,
    0 as campo3
FROM marketplace_configuracoes;

-- 12. Mensagem de conclusão
SELECT 
    '✅ ATUALIZAÇÃO CONCLUÍDA!' as status,
    'Marketplace integrado com sistema principal' as descricao,
    NOW() as data_atualizacao;
