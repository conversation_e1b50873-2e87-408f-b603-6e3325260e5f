-- Ajustes na tabela usuarios para corrigir problemas
-- Execute este SQL no seu banco de dados

-- 1. Adicionar campo ultimo_login se não existir
ALTER TABLE usuarios 
ADD COLUMN ultimo_login DATETIME NULL AFTER ativo;

-- 2. Verificar se a estrutura está correta
DESCRIBE usuarios;

-- 3. Inserir usuário admin se não existir
INSERT IGNORE INTO usuarios (nome, email, senha, nivel_acesso, ativo) 
VALUES ('Administrador', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1);

-- Nota: A senha acima é o hash de "password" - altere após o primeiro login

-- 4. Verificar usuários existentes
SELECT id, nome, email, nivel_acesso, ativo, data_cadastro, ultimo_login FROM usuarios;
