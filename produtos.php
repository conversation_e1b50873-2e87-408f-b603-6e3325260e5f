<?php
session_start();

// Verifica se o usuário está logado, se não, redireciona para a página de login
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: index.php");
    exit;
}

// Inclui o arquivo de conexão com o banco de dados
require_once 'includes/db_connect.php';

// Inicializa a variável de mensagem (para sucesso/erro)
$message = '';
$message_type = ''; // 'success' ou 'danger'

// --- Lógica para Exclusão de Produto ---
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $produto_id_to_delete = $_GET['id'];

    // Prepara a query para exclusão
    $sql_delete = "DELETE FROM produtos WHERE id = ?";

    if ($stmt_delete = $conn->prepare($sql_delete)) {
        $stmt_delete->bind_param("i", $produto_id_to_delete);
        if ($stmt_delete->execute()) {
            $message = "Produto excluído com sucesso!";
            $message_type = "success";
        } else {
            $message = "Erro ao excluir o produto: " . $stmt_delete->error;
            $message_type = "danger";
        }
        $stmt_delete->close();
    } else {
        $message = "Erro na preparação da query de exclusão: " . $conn->error;
        $message_type = "danger";
    }
}


// --- Lógica para buscar todos os produtos ---
$sql_select_produtos = "SELECT id, nome, sku, preco_custo, preco_venda, quantidade_estoque, estoque_minimo, fornecedor FROM produtos ORDER BY nome ASC";
$result_produtos = $conn->query($sql_select_produtos);

// Fecha a conexão com o banco de dados
$conn->close();

include_once 'includes/header.php';
?>

<!-- Page Header -->
<div class="page-header fade-in-up">
    <h1 class="page-title">
        <i class="fas fa-box"></i>
        Gerenciamento de Produtos
    </h1>
    <p class="page-subtitle">Controle completo do seu estoque</p>
</div>

<?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show fade-in-up" role="alert">
        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-6 col-lg-3">
        <div class="stats-card primary fade-in-up">
            <div class="stats-icon primary">
                <i class="fas fa-boxes"></i>
            </div>
            <div class="stats-value"><?php echo $result_produtos->num_rows; ?></div>
            <div class="stats-label">Total de Produtos</div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card danger fade-in-up">
            <div class="stats-icon danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stats-value">
                <?php
                $criticos = 0;
                $result_produtos->data_seek(0);
                while($row = $result_produtos->fetch_assoc()) {
                    if ($row['quantidade_estoque'] <= $row['estoque_minimo']) {
                        $criticos++;
                    }
                }
                echo $criticos;
                ?>
            </div>
            <div class="stats-label">Estoque Crítico</div>
            <?php if ($criticos > 0): ?>
                <div class="stats-change negative">
                    <i class="fas fa-arrow-down"></i> Atenção necessária
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card success fade-in-up">
            <div class="stats-icon success">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stats-value">
                <?php
                $valor_total = 0;
                $result_produtos->data_seek(0);
                while($row = $result_produtos->fetch_assoc()) {
                    $valor_total += $row['preco_venda'] * $row['quantidade_estoque'];
                }
                echo 'R$ ' . number_format($valor_total / 1000, 0) . 'K';
                ?>
            </div>
            <div class="stats-label">Valor em Estoque</div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card info fade-in-up">
            <div class="stats-icon info">
                <i class="fas fa-truck"></i>
            </div>
            <div class="stats-value">
                <?php
                $fornecedores = [];
                $result_produtos->data_seek(0);
                while($row = $result_produtos->fetch_assoc()) {
                    if (!empty($row['fornecedor']) && !in_array($row['fornecedor'], $fornecedores)) {
                        $fornecedores[] = $row['fornecedor'];
                    }
                }
                echo count($fornecedores);
                ?>
            </div>
            <div class="stats-label">Fornecedores</div>
        </div>
    </div>
</div>

<!-- Action Bar -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="modern-card fade-in-up">
            <div class="card-body-modern">
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-between align-items-md-center">
                    <div class="d-flex flex-column flex-sm-row gap-2">
                        <a href="cadastro_produto.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i> Novo Produto
                        </a>
                        <button class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i> Imprimir
                        </button>
                        <button class="btn btn-outline-primary" onclick="exportData()">
                            <i class="fas fa-download me-2"></i> Exportar
                        </button>
                    </div>
                    <div class="d-flex gap-2 flex-grow-1 flex-md-grow-0">
                        <div class="input-group" style="max-width: 300px;">
                            <input type="text" class="form-control" placeholder="Buscar produtos..." id="searchInput">
                            <button class="btn btn-outline-primary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Products Table -->
<div class="modern-card fade-in-up">
    <div class="card-header-modern">
        <i class="fas fa-list"></i>
        Lista de Produtos
        <div class="ms-auto">
            <span class="badge bg-primary"><?php echo $result_produtos->num_rows; ?> produtos</span>
        </div>
    </div>
    <div class="card-body-modern">
        <?php if ($result_produtos->num_rows > 0): ?>
            <!-- Desktop Table -->
            <div class="table-modern d-none d-md-block">
                <table class="table table-hover mb-0" id="produtosTable">
                    <thead>
                        <tr>
                            <th>Produto</th>
                            <th>SKU</th>
                            <th>Preços</th>
                            <th>Estoque</th>
                            <th>Fornecedor</th>
                            <th class="text-center">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $result_produtos->data_seek(0);
                        while($row = $result_produtos->fetch_assoc()) {
                            $estoque_baixo = $row['quantidade_estoque'] <= $row['estoque_minimo'];
                            ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="product-avatar me-3">
                                            <i class="fas fa-box"></i>
                                        </div>
                                        <div>
                                            <div class="fw-semibold">
                                                <?php if ($estoque_baixo): ?>
                                                    <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                                <?php endif; ?>
                                                <?php echo htmlspecialchars($row['nome']); ?>
                                            </div>
                                            <small class="text-muted">#<?php echo $row['id']; ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <code class="bg-light px-2 py-1 rounded"><?php echo htmlspecialchars($row['sku']); ?></code>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-semibold text-success">R$ <?php echo number_format($row['preco_venda'], 2, ',', '.'); ?></div>
                                        <small class="text-muted">Custo: R$ <?php echo number_format($row['preco_custo'], 2, ',', '.'); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <span class="status-badge <?php echo $estoque_baixo ? 'status-warning' : 'status-success'; ?>">
                                            <?php echo $row['quantidade_estoque']; ?>
                                        </span>
                                        <small class="text-muted">/ <?php echo $row['estoque_minimo']; ?></small>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-muted"><?php echo htmlspecialchars($row['fornecedor'] ?: 'N/A'); ?></span>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="cadastro_produto.php?id=<?php echo $row['id']; ?>"
                                           class="btn btn-outline-primary btn-sm" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-outline-danger btn-sm"
                                                onclick="confirmDelete(<?php echo $row['id']; ?>)" title="Excluir">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>

            <!-- Mobile Cards -->
            <div class="d-block d-md-none">
                <?php
                $result_produtos->data_seek(0);
                while($row = $result_produtos->fetch_assoc()) {
                    $estoque_baixo = $row['quantidade_estoque'] <= $row['estoque_minimo'];
                    ?>
                    <div class="mobile-product-card mb-3 <?php echo $estoque_baixo ? 'border-warning' : ''; ?>">
                        <div class="d-flex align-items-start mb-3">
                            <div class="product-avatar me-3">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <?php if ($estoque_baixo): ?>
                                        <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                    <?php endif; ?>
                                    <?php echo htmlspecialchars($row['nome']); ?>
                                </h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">#<?php echo $row['id']; ?></small>
                                    <code class="bg-light px-2 py-1 rounded small"><?php echo htmlspecialchars($row['sku']); ?></code>
                                </div>
                            </div>
                        </div>

                        <div class="row g-3 mb-3">
                            <div class="col-6">
                                <div class="mobile-info-item">
                                    <small class="text-muted">Preço de Venda</small>
                                    <div class="fw-semibold text-success">R$ <?php echo number_format($row['preco_venda'], 2, ',', '.'); ?></div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mobile-info-item">
                                    <small class="text-muted">Estoque</small>
                                    <div>
                                        <span class="status-badge <?php echo $estoque_baixo ? 'status-warning' : 'status-success'; ?>">
                                            <?php echo $row['quantidade_estoque']; ?>
                                        </span>
                                        <small class="text-muted">/ <?php echo $row['estoque_minimo']; ?></small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mobile-info-item">
                                    <small class="text-muted">Custo</small>
                                    <div>R$ <?php echo number_format($row['preco_custo'], 2, ',', '.'); ?></div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mobile-info-item">
                                    <small class="text-muted">Fornecedor</small>
                                    <div><?php echo htmlspecialchars($row['fornecedor'] ?: 'N/A'); ?></div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <a href="cadastro_produto.php?id=<?php echo $row['id']; ?>"
                               class="btn btn-outline-primary btn-sm flex-fill">
                                <i class="fas fa-edit me-1"></i> Editar
                            </a>
                            <button class="btn btn-outline-danger btn-sm"
                                    onclick="confirmDelete(<?php echo $row['id']; ?>)">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                <?php } ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="stats-icon primary mx-auto mb-3">
                    <i class="fas fa-box-open"></i>
                </div>
                <h5 class="text-muted mb-2">Nenhum produto cadastrado</h5>
                <p class="text-muted">Comece adicionando seu primeiro produto ao sistema.</p>
                <a href="cadastro_produto.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Adicionar Primeiro Produto
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function confirmDelete(id) {
    if (confirm('Tem certeza que deseja excluir este produto? Esta ação não pode ser desfeita.')) {
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;

        // Show loading
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        // Simulate loading for better UX
        setTimeout(() => {
            window.location.href = `produtos.php?action=delete&id=${id}`;
        }, 500);
    }
}

function searchProducts() {
    const input = document.getElementById('searchInput');
    const filter = input.value.toLowerCase();

    // Search in desktop table
    const table = document.getElementById('produtosTable');
    if (table) {
        const rows = table.getElementsByTagName('tr');
        for (let i = 1; i < rows.length; i++) {
            const cells = rows[i].getElementsByTagName('td');
            let found = false;

            for (let j = 0; j < cells.length - 1; j++) {
                if (cells[j].textContent.toLowerCase().includes(filter)) {
                    found = true;
                    break;
                }
            }

            rows[i].style.display = found ? '' : 'none';
        }
    }

    // Search in mobile cards
    const mobileCards = document.querySelectorAll('.mobile-product-card');
    mobileCards.forEach(card => {
        const text = card.textContent.toLowerCase();
        card.style.display = text.includes(filter) ? '' : 'none';
    });
}

function exportData() {
    // Simple CSV export
    const table = document.getElementById('produtosTable');
    if (!table) return;

    let csv = 'ID,Nome,SKU,Preço Custo,Preço Venda,Estoque,Estoque Mínimo,Fornecedor\n';

    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        if (row.style.display !== 'none') {
            const cells = row.querySelectorAll('td');
            const data = [
                cells[0].textContent.replace('#', ''),
                cells[0].querySelector('.fw-semibold').textContent.trim(),
                cells[1].textContent.trim(),
                cells[2].querySelector('.text-muted').textContent.replace('Custo: ', ''),
                cells[2].querySelector('.fw-semibold').textContent.trim(),
                cells[3].querySelector('.status-badge').textContent.trim(),
                cells[3].querySelector('.text-muted').textContent.replace('/ ', ''),
                cells[4].textContent.trim()
            ];
            csv += data.map(field => `"${field}"`).join(',') + '\n';
        }
    });

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'produtos.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}

// Real-time search
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', searchProducts);

        // Clear search on escape
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                this.value = '';
                searchProducts();
            }
        });
    }
});
</script>

<?php include_once 'includes/footer.php'; ?>