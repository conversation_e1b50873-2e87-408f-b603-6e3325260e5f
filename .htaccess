# =====================================================
# HTACCESS - Sistema Karla Wollinger
# Configurações de segurança e redirecionamento
# =====================================================

# Habilitar RewriteEngine
RewriteEngine On

# =====================================================
# REDIRECIONAMENTOS E URLs AMIGÁVEIS
# =====================================================

# Redirecionar para login se não estiver logado
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/login\.php$
RewriteCond %{REQUEST_URI} !^/css/
RewriteCond %{REQUEST_URI} !^/js/
RewriteCond %{REQUEST_URI} !^/images/
RewriteCond %{REQUEST_URI} !^/includes/
RewriteRule ^(.*)$ login.php [L]

# Página inicial redireciona para dashboard
RewriteRule ^$ dashboard.php [L]
RewriteRule ^index\.php$ dashboard.php [R=301,L]

# URLs amigáveis (opcional)
RewriteRule ^dashboard/?$ dashboard.php [L]
RewriteRule ^produtos/?$ produtos.php [L]
RewriteRule ^clientes/?$ clientes.php [L]
RewriteRule ^vendas/?$ vendas.php [L]
RewriteRule ^empresas/?$ empresas_representadas.php [L]

# =====================================================
# SEGURANÇA - PROTEÇÃO DE ARQUIVOS
# =====================================================

# Proteger arquivos de configuração
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

<Files "*.ini">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# Proteger diretório includes
<Files "includes/*">
    Order allow,deny
    Deny from all
</Files>

# Permitir apenas arquivos CSS e JS nos diretórios públicos
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# =====================================================
# SEGURANÇA - HEADERS HTTP
# =====================================================

# Prevenir clickjacking
Header always append X-Frame-Options SAMEORIGIN

# Prevenir MIME type sniffing
Header always set X-Content-Type-Options nosniff

# Habilitar proteção XSS
Header always set X-XSS-Protection "1; mode=block"

# Política de referrer
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Content Security Policy básica
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com; img-src 'self' data:;"

# =====================================================
# PERFORMANCE - COMPRESSÃO E CACHE
# =====================================================

# Habilitar compressão GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache para arquivos estáticos
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# =====================================================
# SEGURANÇA - PROTEÇÕES ADICIONAIS
# =====================================================

# Desabilitar listagem de diretórios
Options -Indexes

# Desabilitar execução de PHP em uploads (se houver)
<Directory "uploads">
    php_flag engine off
</Directory>

# Limitar tamanho de upload
php_value upload_max_filesize 10M
php_value post_max_size 10M

# Desabilitar algumas funções perigosas do PHP
php_value disable_functions "exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source"

# =====================================================
# TRATAMENTO DE ERROS
# =====================================================

# Páginas de erro customizadas
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# Não mostrar versão do servidor
ServerTokens Prod

# =====================================================
# PROTEÇÃO CONTRA ATAQUES
# =====================================================

# Bloquear user agents maliciosos
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Bloquear tentativas de SQL injection
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} union.*select.*\( [NC,OR]
    RewriteCond %{QUERY_STRING} union.*all.*select.* [NC,OR]
    RewriteCond %{QUERY_STRING} concat.*\( [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Limitar tentativas de login (básico)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
