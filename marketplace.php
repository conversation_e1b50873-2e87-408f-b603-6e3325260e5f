<?php
session_start();

require_once 'includes/db_connect.php';

// Verificar token de acesso
$token = $_GET['token'] ?? '';
if (empty($token)) {
    die('Acesso negado. Token inválido.');
}

// Validar token e buscar dados do cliente
$sql_token = "SELECT ml.*, c.nome as cliente_nome, c.email as cliente_email, c.endereco, c.cidade, c.estado, c.cep, c.telefone
              FROM marketplace_links ml 
              LEFT JOIN clientes c ON ml.cliente_id = c.id 
              WHERE ml.token_acesso = ? AND ml.ativo = 1";
$stmt_token = $conn->prepare($sql_token);
$stmt_token->bind_param("s", $token);
$stmt_token->execute();
$result_token = $stmt_token->get_result();

if ($result_token->num_rows === 0) {
    die('Link inválido ou expirado.');
}

$link_data = $result_token->fetch_assoc();

// Verificar se o link expirou
if ($link_data['data_expiracao'] && strtotime($link_data['data_expiracao']) < time()) {
    die('Link expirado.');
}

// Atualizar último acesso e contador
$sql_update = "UPDATE marketplace_links SET ultimo_acesso = NOW(), total_acessos = total_acessos + 1 WHERE token_acesso = ?";
$stmt_update = $conn->prepare($sql_update);
$stmt_update->bind_param("s", $token);
$stmt_update->execute();

// Buscar configurações do marketplace
$sql_config = "SELECT chave, valor FROM marketplace_configuracoes";
$result_config = $conn->query($sql_config);
$config = [];
while ($row = $result_config->fetch_assoc()) {
    $config[$row['chave']] = $row['valor'];
}

// Verificar se marketplace está ativo
if (!isset($config['marketplace_ativo']) || $config['marketplace_ativo'] != '1') {
    die('Marketplace temporariamente indisponível.');
}

// Buscar produtos ativos para marketplace
$sql_produtos = "SELECT p.*, e.nome_empresa 
                 FROM produtos p 
                 LEFT JOIN empresas_representadas e ON p.empresa_id = e.id 
                 WHERE p.ativo_marketplace = 1 AND p.quantidade_estoque > 0
                 ORDER BY p.destaque_marketplace DESC, p.ordem_exibicao ASC, p.nome ASC";
$result_produtos = $conn->query($sql_produtos);

// Buscar itens do carrinho
$sql_carrinho = "SELECT mc.*, p.nome as produto_nome, p.preco_venda, p.quantidade_estoque
                 FROM marketplace_carrinho mc
                 LEFT JOIN produtos p ON mc.produto_id = p.id
                 WHERE mc.token_acesso = ?";
$stmt_carrinho = $conn->prepare($sql_carrinho);
$stmt_carrinho->bind_param("s", $token);
$stmt_carrinho->execute();
$result_carrinho = $stmt_carrinho->get_result();

$carrinho_itens = [];
$total_carrinho = 0;
while ($item = $result_carrinho->fetch_assoc()) {
    $carrinho_itens[] = $item;
    $total_carrinho += $item['quantidade'] * $item['preco_unitario'];
}

$total_itens_carrinho = count($carrinho_itens);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($config['titulo_marketplace'] ?? 'Marketplace'); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e293b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
        }
        
        .marketplace-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .product-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .product-image {
            height: 200px;
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            border-radius: 12px 12px 0 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: var(--primary-color);
        }
        
        .btn-add-cart {
            background: var(--primary-color);
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-add-cart:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }
        
        .cart-sidebar {
            position: fixed;
            right: -400px;
            top: 0;
            width: 400px;
            height: 100vh;
            background: white;
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            z-index: 1050;
            overflow-y: auto;
        }
        
        .cart-sidebar.open {
            right: 0;
        }
        
        .cart-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1040;
            display: none;
        }
        
        .cart-overlay.show {
            display: block;
        }
        
        .floating-cart {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1030;
        }
        
        .badge-cart {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="marketplace-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-store me-3"></i>
                        <?php echo htmlspecialchars($config['titulo_marketplace'] ?? 'Marketplace'); ?>
                    </h1>
                    <p class="mb-0 opacity-75">
                        <?php echo htmlspecialchars($config['descricao_marketplace'] ?? ''); ?>
                    </p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="bg-white bg-opacity-10 rounded p-3">
                        <h6 class="mb-1">Bem-vindo,</h6>
                        <strong><?php echo htmlspecialchars($link_data['cliente_nome']); ?></strong>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Filtros e Busca -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Buscar produtos..." id="searchInput">
                    <button class="btn btn-outline-primary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-md-end">
                <select class="form-select d-inline-block w-auto" id="filterEmpresa">
                    <option value="">Todas as empresas</option>
                    <?php
                    // Buscar empresas com produtos ativos
                    $sql_empresas = "SELECT DISTINCT e.id, e.nome_empresa 
                                     FROM empresas_representadas e 
                                     INNER JOIN produtos p ON e.id = p.empresa_id 
                                     WHERE p.ativo_marketplace = 1 
                                     ORDER BY e.nome_empresa";
                    $result_empresas = $conn->query($sql_empresas);
                    while ($empresa = $result_empresas->fetch_assoc()) {
                        echo '<option value="' . $empresa['id'] . '">' . htmlspecialchars($empresa['nome_empresa']) . '</option>';
                    }
                    ?>
                </select>
            </div>
        </div>

        <!-- Produtos -->
        <div class="row" id="produtos-container">
            <?php
            if ($result_produtos && $result_produtos->num_rows > 0) {
                while ($produto = $result_produtos->fetch_assoc()) {
                    ?>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4 produto-item" data-empresa="<?php echo $produto['empresa_id']; ?>" data-nome="<?php echo strtolower($produto['nome']); ?>">
                        <div class="card product-card">
                            <div class="product-image">
                                <?php if ($produto['imagem_url']): ?>
                                    <img src="<?php echo htmlspecialchars($produto['imagem_url']); ?>" alt="<?php echo htmlspecialchars($produto['nome']); ?>" class="img-fluid">
                                <?php else: ?>
                                    <i class="fas fa-box"></i>
                                <?php endif; ?>
                            </div>
                            <div class="card-body">
                                <?php if ($produto['destaque_marketplace']): ?>
                                    <span class="badge bg-warning text-dark mb-2">
                                        <i class="fas fa-star"></i> Destaque
                                    </span>
                                <?php endif; ?>
                                
                                <h6 class="card-title"><?php echo htmlspecialchars($produto['nome']); ?></h6>
                                
                                <p class="text-muted small mb-2">
                                    <i class="fas fa-building me-1"></i>
                                    <?php echo htmlspecialchars($produto['nome_empresa']); ?>
                                </p>
                                
                                <?php if ($produto['descricao']): ?>
                                    <p class="card-text small text-muted">
                                        <?php echo htmlspecialchars(substr($produto['descricao'], 0, 80)) . (strlen($produto['descricao']) > 80 ? '...' : ''); ?>
                                    </p>
                                <?php endif; ?>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="text-primary mb-0">
                                            R$ <?php echo number_format($produto['preco_venda'], 2, ',', '.'); ?>
                                        </h5>
                                        <small class="text-muted">
                                            Estoque: <?php echo $produto['quantidade_estoque']; ?>
                                        </small>
                                    </div>
                                    <button class="btn btn-add-cart" onclick="adicionarAoCarrinho(<?php echo $produto['id']; ?>, '<?php echo htmlspecialchars($produto['nome']); ?>', <?php echo $produto['preco_venda']; ?>, <?php echo $produto['quantidade_estoque']; ?>)">
                                        <i class="fas fa-cart-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } else {
                echo '<div class="col-12 text-center"><p class="text-muted">Nenhum produto disponível no momento.</p></div>';
            }
            ?>
        </div>
    </div>

    <!-- Carrinho Flutuante -->
    <div class="floating-cart">
        <button class="btn btn-primary btn-lg rounded-circle" onclick="toggleCarrinho()" id="cart-button">
            <i class="fas fa-shopping-cart"></i>
            <span class="badge-cart" id="cart-count"><?php echo $total_itens_carrinho; ?></span>
        </button>
    </div>

    <!-- Overlay do Carrinho -->
    <div class="cart-overlay" id="cart-overlay" onclick="toggleCarrinho()"></div>

    <!-- Sidebar do Carrinho -->
    <div class="cart-sidebar" id="cart-sidebar">
        <div class="p-4 border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Meu Carrinho
                </h5>
                <button class="btn btn-link text-dark" onclick="toggleCarrinho()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        
        <div class="p-4" id="cart-content">
            <!-- Conteúdo do carrinho será carregado via JavaScript -->
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const token = '<?php echo $token; ?>';
        let carrinho = <?php echo json_encode($carrinho_itens); ?>;
        
        // Carregar carrinho na inicialização
        document.addEventListener('DOMContentLoaded', function() {
            atualizarCarrinho();
        });
    </script>
    <script src="js/marketplace.js"></script>
</body>
</html>

<?php $conn->close(); ?>
