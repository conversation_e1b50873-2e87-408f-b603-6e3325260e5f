/* Importar Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #f8fafc;
    color: #334155;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Forçar layout correto */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

/* Remover estilos conflitantes do Bootstrap */
.container, .container-fluid, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: 100% !important;
    width: 100% !important;
}

/* Container principal */
.main-container {
    padding: 0;
    margin: 0;
    max-width: 100%;
    background: transparent;
}

/* Forçar posicionamento correto */
body.dashboard-layout {
    margin: 0;
    padding: 0;
    background: #f8fafc;
}

body.dashboard-layout .main-content {
    margin-left: 280px !important;
    margin-top: 80px !important;
    padding: 2rem !important;
    background: #f8fafc !important;
    min-height: calc(100vh - 80px) !important;
}

/* Garantir que sidebar e topbar estejam visíveis */
.sidebar {
    display: block !important;
    position: fixed !important;
    z-index: 1000 !important;
}

.topbar {
    display: flex !important;
    position: fixed !important;
    z-index: 999 !important;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    z-index: 1000;
    transition: all 0.3s ease;
    overflow-y: auto;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar.collapsed .sidebar-brand span,
.sidebar.collapsed .nav-link span {
    display: none;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.sidebar-brand {
    color: #fff;
    font-size: 1.5rem;
    font-weight: 700;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.sidebar-brand:hover {
    color: #fff;
    text-decoration: none;
}

.sidebar-brand i {
    color: #3b82f6;
    font-size: 2rem;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    margin: 0.25rem 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.875rem 1rem;
    color: #94a3b8;
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
}

.nav-link:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    transform: translateX(4px);
    text-decoration: none;
}

.nav-link.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #fff;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.nav-link i {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

/* Topbar */
.topbar {
    position: fixed;
    top: 0;
    left: 280px;
    right: 0;
    height: 80px;
    background: #fff;
    border-bottom: 1px solid #e2e8f0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.topbar.expanded {
    left: 80px;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #64748b;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: #f1f5f9;
    color: #3b82f6;
}

.topbar-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    text-align: right;
}

.user-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.875rem;
}

.user-role {
    font-size: 0.75rem;
    color: #64748b;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-weight: 600;
}

/* Main Content */
.main-content {
    margin-left: 280px;
    margin-top: 80px;
    padding: 2rem;
    min-height: calc(100vh - 80px);
    transition: all 0.3s ease;
    background: #f8fafc;
}

.main-content.expanded {
    margin-left: 80px;
}

/* Garantir que o conteúdo não seja sobreposto */
body {
    margin: 0;
    padding: 0;
}

.container, .container-fluid {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: 100% !important;
}

/* Page Header */
.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-title i {
    color: #3b82f6;
}

.page-subtitle {
    color: #64748b;
    font-size: 1rem;
}

/* Cards Modernos */
.modern-card {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.card-header-modern {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-body-modern {
    padding: 2rem;
}

/* Stats Cards */
.stats-card {
    background: #fff;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--card-color, #3b82f6);
}

.stats-card.primary::before { background: #3b82f6; }
.stats-card.success::before { background: #10b981; }
.stats-card.warning::before { background: #f59e0b; }
.stats-card.danger::before { background: #ef4444; }
.stats-card.info::before { background: #06b6d4; }

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stats-icon.primary { background: rgba(59, 130, 246, 0.1); color: #3b82f6; }
.stats-icon.success { background: rgba(16, 185, 129, 0.1); color: #10b981; }
.stats-icon.warning { background: rgba(245, 158, 11, 0.1); color: #f59e0b; }
.stats-icon.danger { background: rgba(239, 68, 68, 0.1); color: #ef4444; }
.stats-icon.info { background: rgba(6, 182, 212, 0.1); color: #06b6d4; }

.stats-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e293b;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stats-label {
    color: #64748b;
    font-weight: 500;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-change {
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.5rem;
}

.stats-change.positive { color: #10b981; }
.stats-change.negative { color: #ef4444; }

/* Botões */
.btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #fff;
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #fff;
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: #fff;
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: #fff;
}

.btn-outline-primary {
    border: 2px solid #3b82f6;
    color: #3b82f6;
    background: transparent;
}

.btn-outline-primary:hover {
    background: #3b82f6;
    color: #fff;
}

/* Tabelas */
.table-modern {
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-modern thead th {
    background: #f8fafc;
    border: none;
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-modern tbody td {
    padding: 1.25rem 1.5rem;
    border: none;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.table-modern tbody tr:hover {
    background: #f8fafc;
}

/* Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-success {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.status-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.status-info {
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-overlay.show {
    display: block;
    opacity: 1;
}

/* Responsividade */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .topbar {
        left: 0 !important;
    }

    .main-content {
        margin-left: 0 !important;
    }
}

@media (max-width: 768px) {
    .topbar {
        padding: 0 1rem;
    }

    .main-content {
        padding: 1rem;
        margin-top: 80px;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .stats-card {
        padding: 1.5rem;
    }

    .stats-value {
        font-size: 2rem;
    }

    .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .card-body-modern {
        padding: 1.5rem;
    }

    .table-modern thead th,
    .table-modern tbody td {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .topbar {
        height: 70px;
        padding: 0 0.75rem;
    }

    .main-content {
        margin-top: 70px;
        padding: 0.75rem;
    }

    .page-title {
        font-size: 1.25rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .stats-card {
        padding: 1rem;
    }

    .stats-value {
        font-size: 1.75rem;
    }

    .stats-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .user-info {
        display: none;
    }

    .sidebar-brand span {
        display: none;
    }

    .sidebar.collapsed .nav-link span {
        display: none;
    }
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

/* Gráficos */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* Forms */
.form-control {
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

/* Alerts */
.alert {
    border-radius: 12px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
}

/* Utilities */
.text-muted {
    color: #64748b !important;
}

.text-primary {
    color: #3b82f6 !important;
}

.text-success {
    color: #10b981 !important;
}

.text-danger {
    color: #ef4444 !important;
}

.text-warning {
    color: #f59e0b !important;
}

/* Loading */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(59, 130, 246, 0.3);
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Login Page */
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    width: 100%;
    max-width: 400px;
}