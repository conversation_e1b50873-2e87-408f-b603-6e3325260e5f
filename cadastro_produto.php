<?php
session_start();

// Verifica se o usuário está logado
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: index.php");
    exit;
}

require_once 'includes/db_connect.php';

$id = $nome = $descricao = $sku = $preco_custo = $preco_venda = $quantidade_estoque = $estoque_minimo = $fornecedor = $empresa_id = "";
$title = "Cadastrar Novo Produto";
$submit_button_text = "Cadastrar Produto";
$message = '';
$message_type = '';

// Buscar empresas representadas
$sql_empresas = "SELECT id, nome_empresa FROM empresas_representadas WHERE status = 'ativo' ORDER BY nome_empresa ASC";
$result_empresas = $conn->query($sql_empresas);

// Processar formulário quando enviado
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Coleta e sanitiza os dados do formulário
    $id = trim($_POST["id"] ?? ''); // Pode vir vazio para novo cadastro
    $nome = trim($_POST["nome"]);
    $descricao = trim($_POST["descricao"]);
    $sku = trim($_POST["sku"]);
    // Usar str_replace para converter vírgulas em pontos para valores decimais
    $preco_custo = str_replace(',', '.', trim($_POST["preco_custo"]));
    $preco_venda = str_replace(',', '.', trim($_POST["preco_venda"]));
    $quantidade_estoque = trim($_POST["quantidade_estoque"]);
    $estoque_minimo = trim($_POST["estoque_minimo"]);
    $fornecedor = trim($_POST["fornecedor"]);
    $empresa_id = trim($_POST["empresa_id"]);

    // Validação básica (pode ser aprimorada)
    if (empty($nome) || empty($sku) || empty($empresa_id) || !is_numeric($preco_custo) || !is_numeric($preco_venda) || !is_numeric($quantidade_estoque) || !is_numeric($estoque_minimo)) {
        $message = "Por favor, preencha todos os campos obrigatórios e garanta que os valores numéricos estejam corretos.";
        $message_type = "danger";
    } else {
        if (empty($id)) { // Novo Produto
            $sql = "INSERT INTO produtos (nome, descricao, sku, preco_custo, preco_venda, quantidade_estoque, estoque_minimo, fornecedor, empresa_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            if ($stmt = $conn->prepare($sql)) {
                $stmt->bind_param("sssdiiisi", $nome, $descricao, $sku, $preco_custo, $preco_venda, $quantidade_estoque, $estoque_minimo, $fornecedor, $empresa_id);
                if ($stmt->execute()) {
                    $message = "Produto cadastrado com sucesso!";
                    $message_type = "success";
                    // Limpa os campos após o cadastro bem-sucedido
                    $nome = $descricao = $sku = $preco_custo = $preco_venda = $quantidade_estoque = $estoque_minimo = $fornecedor = $empresa_id = "";
                } else {
                    $message = "Erro ao cadastrar produto: " . $stmt->error;
                    $message_type = "danger";
                }
                $stmt->close();
            } else {
                $message = "Erro na preparação da query de inserção: " . $conn->error;
                $message_type = "danger";
            }
        } else { // Editar Produto Existente
            $sql = "UPDATE produtos SET nome = ?, descricao = ?, sku = ?, preco_custo = ?, preco_venda = ?, quantidade_estoque = ?, estoque_minimo = ?, fornecedor = ?, empresa_id = ? WHERE id = ?";
            if ($stmt = $conn->prepare($sql)) {
                $stmt->bind_param("sssdiiisii", $nome, $descricao, $sku, $preco_custo, $preco_venda, $quantidade_estoque, $estoque_minimo, $fornecedor, $empresa_id, $id);
                if ($stmt->execute()) {
                    $message = "Produto atualizado com sucesso!";
                    $message_type = "success";
                } else {
                    $message = "Erro ao atualizar produto: " . $stmt->error;
                    $message_type = "danger";
                }
                $stmt->close();
            } else {
                $message = "Erro na preparação da query de atualização: " . $conn->error;
                $message_type = "danger";
            }
        }
    }
}

// Preencher formulário para edição se um ID for passado via GET
if (isset($_GET["id"]) && empty($message)) { // 'empty($message)' para não sobrescrever se houver erro pós-submissão
    $id = trim($_GET["id"]);
    $sql_edit = "SELECT id, nome, descricao, sku, preco_custo, preco_venda, quantidade_estoque, estoque_minimo, fornecedor, empresa_id FROM produtos WHERE id = ?";
    if ($stmt_edit = $conn->prepare($sql_edit)) {
        $stmt_edit->bind_param("i", $id);
        if ($stmt_edit->execute()) {
            $result_edit = $stmt_edit->get_result();
            if ($result_edit->num_rows == 1) {
                $row = $result_edit->fetch_assoc();
                $nome = $row['nome'];
                $descricao = $row['descricao'];
                $sku = $row['sku'];
                $preco_custo = number_format($row['preco_custo'], 2, ',', '.'); // Formata para exibição
                $preco_venda = number_format($row['preco_venda'], 2, ',', '.'); // Formata para exibição
                $quantidade_estoque = $row['quantidade_estoque'];
                $estoque_minimo = $row['estoque_minimo'];
                $fornecedor = $row['fornecedor'];
                $empresa_id = $row['empresa_id'];

                $title = "Editar Produto";
                $submit_button_text = "Atualizar Produto";
            } else {
                $message = "Produto não encontrado.";
                $message_type = "danger";
                $id = ""; // Limpa o ID para tratar como novo cadastro caso não encontre
            }
        } else {
            $message = "Erro ao buscar produto para edição: " . $stmt_edit->error;
            $message_type = "danger";
        }
        $stmt_edit->close();
    }
}

$conn->close(); // Fechar a conexão apenas uma vez, após todas as operações

include_once 'includes/header.php';
?>

<h2 class="mb-4"><i class="fas fa-<?php echo ($id ? 'edit' : 'plus-circle'); ?> me-2"></i> <?php echo $title; ?></h2>

<?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card shadow-sm mb-4">
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
            <input type="hidden" name="id" value="<?php echo htmlspecialchars($id); ?>">

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="empresa_id" class="form-label">Empresa Representada <span class="text-danger">*</span></label>
                    <select class="form-control" id="empresa_id" name="empresa_id" required>
                        <option value="">Selecione a empresa...</option>
                        <?php
                        if ($result_empresas && $result_empresas->num_rows > 0) {
                            while($empresa = $result_empresas->fetch_assoc()) {
                                $selected = ($empresa['id'] == $empresa_id) ? 'selected' : '';
                                echo "<option value='{$empresa['id']}' {$selected}>" . htmlspecialchars($empresa['nome_empresa']) . "</option>";
                            }
                        }
                        ?>
                    </select>
                    <?php if (!$result_empresas || $result_empresas->num_rows == 0): ?>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            <a href="cadastro_empresa.php">Cadastre uma empresa</a> antes de adicionar produtos.
                        </small>
                    <?php endif; ?>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="nome" class="form-label">Nome do Produto <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="nome" name="nome" value="<?php echo htmlspecialchars($nome); ?>" required>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="sku" class="form-label">SKU (Código) <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="sku" name="sku" value="<?php echo htmlspecialchars($sku); ?>" required>
                </div>
                <div class="col-md-8 mb-3">
                    <label for="fornecedor" class="form-label">Fornecedor</label>
                    <input type="text" class="form-control" id="fornecedor" name="fornecedor" value="<?php echo htmlspecialchars($fornecedor); ?>" placeholder="Nome do fornecedor original">
                </div>
            </div>

            <div class="mb-3">
                <label for="descricao" class="form-label">Descrição</label>
                <textarea class="form-control" id="descricao" name="descricao" rows="3"><?php echo htmlspecialchars($descricao); ?></textarea>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="preco_custo" class="form-label">Preço de Custo (R$) <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="preco_custo" name="preco_custo" value="<?php echo htmlspecialchars($preco_custo); ?>" required placeholder="0,00">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="preco_venda" class="form-label">Preço de Venda (R$) <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="preco_venda" name="preco_venda" value="<?php echo htmlspecialchars($preco_venda); ?>" required placeholder="0,00">
                </div>
                <div class="col-md-2 mb-3">
                    <label for="quantidade_estoque" class="form-label">Estoque Atual <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="quantidade_estoque" name="quantidade_estoque" value="<?php echo htmlspecialchars($quantidade_estoque); ?>" required min="0">
                </div>
                <div class="col-md-2 mb-3">
                    <label for="estoque_minimo" class="form-label">Estoque Mínimo <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="estoque_minimo" name="estoque_minimo" value="<?php echo htmlspecialchars($estoque_minimo); ?>" required min="0">
                </div>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <button type="submit" class="btn btn-success btn-lg">
                    <i class="fas fa-<?php echo ($id ? 'save' : 'plus'); ?> me-2"></i> <?php echo $submit_button_text; ?>
                </button>
                <a href="produtos.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i> Voltar para Produtos
                </a>
            </div>
        </form>
    </div>
</div>

<?php include_once 'includes/footer.php'; ?>