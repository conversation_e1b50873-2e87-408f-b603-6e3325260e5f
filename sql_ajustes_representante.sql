-- =====================================================
-- AJUSTES PARA SISTEMA DE REPRESENTANTE COMERCIAL
-- <PERSON><PERSON><PERSON>
-- =====================================================

-- 1. CRIAR TABELA DE EMPRESAS REPRESENTADAS
CREATE TABLE empresas_representadas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome_empresa VARCHAR(255) NOT NULL,
    razao_social VARCHAR(255),
    cnpj VARCHAR(18) UNIQUE,
    endereco TEXT,
    cidade VARCHAR(100),
    estado VARCHAR(2),
    cep VARCHAR(10),
    telefone VARCHAR(20),
    email VARCHAR(100),
    contato_responsavel VARCHAR(100),
    telefone_responsavel VARCHAR(20),
    email_responsavel VARCHAR(100),
    comissao_padrao DECIMAL(5,2) DEFAULT 0.00,
    observacoes TEXT,
    status ENUM('ativo', 'inativo') DEFAULT 'ativo',
    data_inicio_representacao DATE,
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. ADICIONAR COLUNA empresa_id NA TABELA PRODUTOS
ALTER TABLE produtos 
ADD COLUMN empresa_id INT,
ADD CONSTRAINT fk_produtos_empresa 
    FOREIGN KEY (empresa_id) REFERENCES empresas_representadas(id);

-- 3. ADICIONAR COLUNAS NA TABELA VENDAS
ALTER TABLE vendas 
ADD COLUMN empresa_id INT,
ADD COLUMN comissao_percentual DECIMAL(5,2) DEFAULT 0.00,
ADD COLUMN valor_comissao DECIMAL(10,2) DEFAULT 0.00,
ADD CONSTRAINT fk_vendas_empresa 
    FOREIGN KEY (empresa_id) REFERENCES empresas_representadas(id);

-- 4. ADICIONAR COLUNAS NA TABELA ORCAMENTOS
ALTER TABLE orcamentos 
ADD COLUMN empresa_id INT,
ADD COLUMN comissao_percentual DECIMAL(5,2) DEFAULT 0.00,
ADD COLUMN valor_comissao DECIMAL(10,2) DEFAULT 0.00,
ADD CONSTRAINT fk_orcamentos_empresa 
    FOREIGN KEY (empresa_id) REFERENCES empresas_representadas(id);

-- 5. CRIAR TABELA DE MOVIMENTAÇÕES FINANCEIRAS POR EMPRESA
CREATE TABLE movimentacoes_financeiras (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_id INT NOT NULL,
    tipo ENUM('entrada', 'saida') NOT NULL,
    categoria ENUM('comissao', 'despesa', 'investimento', 'outros') NOT NULL,
    descricao VARCHAR(255) NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    data_movimentacao DATE NOT NULL,
    venda_id INT NULL,
    orcamento_id INT NULL,
    observacoes TEXT,
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (empresa_id) REFERENCES empresas_representadas(id),
    FOREIGN KEY (venda_id) REFERENCES vendas(id),
    FOREIGN KEY (orcamento_id) REFERENCES orcamentos(id)
);

-- 6. CRIAR TABELA DE METAS POR EMPRESA
CREATE TABLE metas_vendas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_id INT NOT NULL,
    mes INT NOT NULL,
    ano INT NOT NULL,
    meta_valor DECIMAL(10,2) NOT NULL,
    meta_quantidade INT DEFAULT 0,
    valor_alcancado DECIMAL(10,2) DEFAULT 0.00,
    quantidade_alcancada INT DEFAULT 0,
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (empresa_id) REFERENCES empresas_representadas(id),
    UNIQUE KEY unique_meta_empresa_mes_ano (empresa_id, mes, ano)
);

-- 7. ATUALIZAR TABELA TRANSACOES_FINANCEIRAS (se existir)
-- Adicionar empresa_id para separar por empresa
ALTER TABLE transacoes_financeiras 
ADD COLUMN empresa_id INT,
ADD CONSTRAINT fk_transacoes_empresa 
    FOREIGN KEY (empresa_id) REFERENCES empresas_representadas(id);

-- 8. CRIAR ÍNDICES PARA PERFORMANCE
CREATE INDEX idx_produtos_empresa ON produtos(empresa_id);
CREATE INDEX idx_vendas_empresa ON vendas(empresa_id);
CREATE INDEX idx_orcamentos_empresa ON orcamentos(empresa_id);
CREATE INDEX idx_movimentacoes_empresa ON movimentacoes_financeiras(empresa_id);
CREATE INDEX idx_movimentacoes_data ON movimentacoes_financeiras(data_movimentacao);

-- 9. INSERIR DADOS EXEMPLO (OPCIONAL)
INSERT INTO empresas_representadas (
    nome_empresa, 
    razao_social, 
    cnpj, 
    cidade, 
    estado, 
    contato_responsavel, 
    comissao_padrao,
    data_inicio_representacao
) VALUES 
('Empresa Exemplo 1', 'Empresa Exemplo 1 Ltda', '12.345.678/0001-90', 'São Paulo', 'SP', 'João Silva', 5.00, '2024-01-01'),
('Empresa Exemplo 2', 'Empresa Exemplo 2 S.A.', '98.765.432/0001-10', 'Rio de Janeiro', 'RJ', 'Maria Santos', 7.50, '2024-01-01');

-- 10. CRIAR VIEW PARA RELATÓRIOS CONSOLIDADOS
CREATE VIEW vw_vendas_por_empresa AS
SELECT 
    e.nome_empresa,
    e.id as empresa_id,
    COUNT(v.id) as total_vendas,
    SUM(v.valor_total) as valor_total_vendas,
    SUM(v.valor_comissao) as total_comissoes,
    AVG(v.comissao_percentual) as media_comissao
FROM empresas_representadas e
LEFT JOIN vendas v ON e.id = v.empresa_id AND v.status_venda = 'concluida'
GROUP BY e.id, e.nome_empresa;

-- 11. CRIAR VIEW PARA ESTOQUE POR EMPRESA
CREATE VIEW vw_estoque_por_empresa AS
SELECT 
    e.nome_empresa,
    e.id as empresa_id,
    COUNT(p.id) as total_produtos,
    SUM(p.quantidade_estoque) as total_estoque,
    SUM(CASE WHEN p.quantidade_estoque <= p.estoque_minimo THEN 1 ELSE 0 END) as produtos_criticos,
    SUM(p.quantidade_estoque * p.preco_venda) as valor_estoque
FROM empresas_representadas e
LEFT JOIN produtos p ON e.id = p.empresa_id
GROUP BY e.id, e.nome_empresa;

-- =====================================================
-- COMANDOS PARA EXECUTAR APÓS OS AJUSTES
-- =====================================================

-- Atualizar produtos existentes (definir empresa padrão)
-- UPDATE produtos SET empresa_id = 1 WHERE empresa_id IS NULL;

-- Atualizar vendas existentes (definir empresa padrão)
-- UPDATE vendas SET empresa_id = 1 WHERE empresa_id IS NULL;

-- Atualizar orçamentos existentes (definir empresa padrão)
-- UPDATE orcamentos SET empresa_id = 1 WHERE empresa_id IS NULL;

-- =====================================================
-- VERIFICAÇÕES FINAIS
-- =====================================================

-- Verificar estrutura das tabelas
-- DESCRIBE empresas_representadas;
-- DESCRIBE produtos;
-- DESCRIBE vendas;
-- DESCRIBE orcamentos;
-- DESCRIBE movimentacoes_financeiras;

-- Verificar views criadas
-- SELECT * FROM vw_vendas_por_empresa;
-- SELECT * FROM vw_estoque_por_empresa;
