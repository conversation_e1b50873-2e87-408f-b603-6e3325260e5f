-- Ajustes na tabela orcamentos para corrigir problemas
-- Execute este SQL no seu banco de dados

-- 1. Verificar estrutura atual da tabela
DESCRIBE orcamentos;

-- 2. Adicionar campo data_criacao se não existir
ALTER TABLE orcamentos 
ADD COLUMN data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP AFTER observacoes;

-- 3. Atualizar registros existentes que não têm data_criacao
UPDATE orcamentos 
SET data_criacao = CURRENT_TIMESTAMP 
WHERE data_criacao IS NULL;

-- 4. Verificar se a estrutura está correta agora
DESCRIBE orcamentos;

-- 5. Verificar orçamentos existentes
SELECT id, cliente_id, valor_total, status_orcamento, data_criacao, observacoes 
FROM orcamentos 
ORDER BY data_criacao DESC;

-- Nota: Se você já tem orçamentos sem data_criacao, eles receberão a data atual
-- Para preservar a ordem cronológica real, você pode ajustar manualmente se necessário
