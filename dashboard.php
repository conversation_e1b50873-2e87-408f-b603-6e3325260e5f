<?php
// Ativa a exibição de erros para depuração. REMOVA EM AMBIENTE DE PRODUÇÃO!
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Inicia a sessão. É o primeiro passo para gerenciar o login do usuário.
session_start();

// Verifica se o usuário está logado. Se não estiver, redireciona para a página de login (index.php).
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: index.php");
    exit; // Termina o script para garantir que o redirecionamento ocorra.
}

// Inclui o arquivo de conexão com o banco de dados.
require_once 'includes/db_connect.php';

// --- Lógica para buscar dados dinâmicos para o Dashboard ---

// 1. Total de Vendas Concluídas Hoje
$total_vendas_hoje = 0;
$sql_vendas_hoje = "SELECT SUM(valor_total) FROM vendas WHERE status_venda = 'concluida' AND DATE(data_venda) = CURDATE()";
if ($result_vendas_hoje = $conn->query($sql_vendas_hoje)) {
    $row = $result_vendas_hoje->fetch_row();
    $total_vendas_hoje = $row[0] ?? 0;
    $result_vendas_hoje->close();
}

// 2. Total de Vendas do Mês
$total_vendas_mes = 0;
$sql_vendas_mes = "SELECT SUM(valor_total) FROM vendas WHERE status_venda = 'concluida' AND MONTH(data_venda) = MONTH(CURDATE()) AND YEAR(data_venda) = YEAR(CURDATE())";
if ($result_vendas_mes = $conn->query($sql_vendas_mes)) {
    $row = $result_vendas_mes->fetch_row();
    $total_vendas_mes = $row[0] ?? 0;
    $result_vendas_mes->close();
}

// 3. Número de Produtos com Estoque Crítico
$produtos_criticos = 0;
$sql_produtos_criticos = "SELECT COUNT(*) FROM produtos WHERE quantidade_estoque <= estoque_minimo";
if ($result_produtos_criticos = $conn->query($sql_produtos_criticos)) {
    $row = $result_produtos_criticos->fetch_row();
    $produtos_criticos = $row[0] ?? 0;
    $result_produtos_criticos->close();
}

// 4. Total de Produtos
$total_produtos = 0;
$sql_total_produtos = "SELECT COUNT(*) FROM produtos";
if ($result_total_produtos = $conn->query($sql_total_produtos)) {
    $row = $result_total_produtos->fetch_row();
    $total_produtos = $row[0] ?? 0;
    $result_total_produtos->close();
}

// 5. Número de Agendamentos Próximos
$agendamentos_proximos = 0;
$sql_agendamentos_proximos = "SELECT COUNT(*) FROM agendamentos_entrega WHERE data_hora_entrega BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) AND (status_entrega = 'agendado' OR status_entrega = 'em_rota')";
if ($result_agendamentos_proximos = $conn->query($sql_agendamentos_proximos)) {
    $row = $result_agendamentos_proximos->fetch_row();
    $agendamentos_proximos = $row[0] ?? 0;
    $result_agendamentos_proximos->close();
}

// 6. Total de Clientes
$total_clientes = 0;
$sql_total_clientes = "SELECT COUNT(*) FROM clientes";
if ($result_total_clientes = $conn->query($sql_total_clientes)) {
    $row = $result_total_clientes->fetch_row();
    $total_clientes = $row[0] ?? 0;
    $result_total_clientes->close();
}

// 7. Saldo Total Atual
$saldo_total = 0;
$sql_saldo_total = "SELECT tipo, SUM(valor) AS total_valor FROM transacoes_financeiras GROUP BY tipo";
if ($result_saldo_total = $conn->query($sql_saldo_total)) {
    while($row_saldo = $result_saldo_total->fetch_assoc()) {
        if ($row_saldo['tipo'] == 'entrada') {
            $saldo_total += $row_saldo['total_valor'];
        } else {
            $saldo_total -= $row_saldo['total_valor'];
        }
    }
    $result_saldo_total->close();
}

// 8. Orçamentos Pendentes
$orcamentos_pendentes = 0;
$sql_orcamentos_pendentes = "SELECT COUNT(*) FROM orcamentos WHERE status_orcamento = 'pendente'";
if ($result_orcamentos_pendentes = $conn->query($sql_orcamentos_pendentes)) {
    $row = $result_orcamentos_pendentes->fetch_row();
    $orcamentos_pendentes = $row[0] ?? 0;
    $result_orcamentos_pendentes->close();
}

// 9. Vendas dos últimos 7 dias para gráfico
$vendas_7_dias = [];
$sql_vendas_7_dias = "SELECT DATE(data_venda) as data, SUM(valor_total) as total
                      FROM vendas
                      WHERE status_venda = 'concluida'
                      AND data_venda >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
                      GROUP BY DATE(data_venda)
                      ORDER BY data_venda";
if ($result_vendas_7_dias = $conn->query($sql_vendas_7_dias)) {
    while($row = $result_vendas_7_dias->fetch_assoc()) {
        $vendas_7_dias[] = $row;
    }
    $result_vendas_7_dias->close();
}

// 10. Últimas vendas
$ultimas_vendas = [];
$sql_ultimas_vendas = "SELECT v.id, v.data_venda, v.valor_total, c.nome as cliente_nome, v.status_venda
                       FROM vendas v
                       LEFT JOIN clientes c ON v.cliente_id = c.id
                       ORDER BY v.data_venda DESC
                       LIMIT 5";
if ($result_ultimas_vendas = $conn->query($sql_ultimas_vendas)) {
    while($row = $result_ultimas_vendas->fetch_assoc()) {
        $ultimas_vendas[] = $row;
    }
    $result_ultimas_vendas->close();
}

// 11. Produtos com estoque baixo
$produtos_estoque_baixo = [];
$sql_produtos_estoque_baixo = "SELECT nome, quantidade_estoque, estoque_minimo
                               FROM produtos
                               WHERE quantidade_estoque <= estoque_minimo
                               ORDER BY quantidade_estoque ASC
                               LIMIT 5";
if ($result_produtos_estoque_baixo = $conn->query($sql_produtos_estoque_baixo)) {
    while($row = $result_produtos_estoque_baixo->fetch_assoc()) {
        $produtos_estoque_baixo[] = $row;
    }
    $result_produtos_estoque_baixo->close();
}

// Fechar a conexão com o banco de dados
$conn->close();

// Inclui o cabeçalho da página
include_once 'includes/header.php';
?>

<!-- Page Header -->
<div class="page-header fade-in-up">
    <h1 class="page-title">
        <i class="fas fa-tachometer-alt"></i>
        Dashboard
    </h1>
    <p class="page-subtitle">Visão geral do seu negócio</p>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-6 col-lg-3">
        <div class="stats-card primary fade-in-up">
            <div class="stats-icon primary">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stats-value">R$ <?php echo number_format($total_vendas_hoje, 0, ',', '.'); ?></div>
            <div class="stats-label">Vendas Hoje</div>
            <div class="stats-change positive">
                <i class="fas fa-arrow-up"></i> +12% vs ontem
            </div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card success fade-in-up">
            <div class="stats-icon success">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stats-value">R$ <?php echo number_format($total_vendas_mes, 0, ',', '.'); ?></div>
            <div class="stats-label">Vendas do Mês</div>
            <div class="stats-change positive">
                <i class="fas fa-arrow-up"></i> +8% vs mês anterior
            </div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card warning fade-in-up">
            <div class="stats-icon warning">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stats-value"><?php echo $produtos_criticos; ?></div>
            <div class="stats-label">Estoque Crítico</div>
            <div class="stats-change negative">
                <i class="fas fa-arrow-down"></i> de <?php echo $total_produtos; ?> produtos
            </div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card info fade-in-up">
            <div class="stats-icon info">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stats-value">R$ <?php echo number_format($saldo_total, 0, ',', '.'); ?></div>
            <div class="stats-label">Saldo Atual</div>
            <div class="stats-change positive">
                <i class="fas fa-arrow-up"></i> +5% este mês
            </div>
        </div>
    </div>
</div>

<!-- Secondary Stats -->
<div class="row g-4 mb-4">
    <div class="col-6 col-md-4">
        <div class="stats-card primary fade-in-up">
            <div class="stats-icon primary">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-value"><?php echo $total_clientes; ?></div>
            <div class="stats-label">Total de Clientes</div>
        </div>
    </div>

    <div class="col-6 col-md-4">
        <div class="stats-card info fade-in-up">
            <div class="stats-icon info">
                <i class="fas fa-truck"></i>
            </div>
            <div class="stats-value"><?php echo $agendamentos_proximos; ?></div>
            <div class="stats-label">Entregas Próximas</div>
        </div>
    </div>

    <div class="col-12 col-md-4">
        <div class="stats-card warning fade-in-up">
            <div class="stats-icon warning">
                <i class="fas fa-file-invoice-dollar"></i>
            </div>
            <div class="stats-value"><?php echo $orcamentos_pendentes; ?></div>
            <div class="stats-label">Orçamentos Pendentes</div>
        </div>
    </div>
</div>

<!-- Charts and Tables -->
<div class="row g-4 mb-4">
    <!-- Sales Chart -->
    <div class="col-12 col-lg-8">
        <div class="modern-card fade-in-up">
            <div class="card-header-modern">
                <i class="fas fa-chart-line"></i>
                Vendas dos Últimos 7 Dias
            </div>
            <div class="card-body-modern">
                <div class="chart-container">
                    <canvas id="vendasChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Products -->
    <div class="col-12 col-lg-4">
        <div class="modern-card fade-in-up">
            <div class="card-header-modern">
                <i class="fas fa-exclamation-triangle"></i>
                Produtos com Estoque Baixo
            </div>
            <div class="card-body-modern">
                <?php if (empty($produtos_estoque_baixo)): ?>
                    <div class="text-center py-4">
                        <div class="stats-icon success mx-auto mb-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h6 class="text-muted">Estoque OK!</h6>
                        <p class="text-muted small">Todos os produtos estão com estoque adequado.</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-3">
                        <?php foreach ($produtos_estoque_baixo as $produto): ?>
                            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                <div>
                                    <h6 class="mb-1 fw-semibold"><?php echo htmlspecialchars($produto['nome']); ?></h6>
                                    <small class="text-muted">Mínimo: <?php echo $produto['estoque_minimo']; ?></small>
                                </div>
                                <span class="status-badge status-danger">
                                    <?php echo $produto['quantidade_estoque']; ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="text-center mt-3">
                        <a href="produtos.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-boxes me-1"></i> Ver Produtos
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Sales -->
<div class="row g-4">
    <div class="col-12">
        <div class="modern-card fade-in-up">
            <div class="card-header-modern">
                <i class="fas fa-shopping-cart"></i>
                Últimas Vendas
                <div class="ms-auto">
                    <a href="registrar_venda.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i> Nova Venda
                    </a>
                </div>
            </div>
            <div class="card-body-modern">
                <?php if (empty($ultimas_vendas)): ?>
                    <div class="text-center py-5">
                        <div class="stats-icon primary mx-auto mb-3">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h5 class="text-muted mb-2">Nenhuma venda registrada</h5>
                        <p class="text-muted">Comece registrando sua primeira venda no sistema.</p>
                        <a href="registrar_venda.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i> Registrar Primeira Venda
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-modern">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Cliente</th>
                                    <th>Data</th>
                                    <th>Valor</th>
                                    <th>Status</th>
                                    <th class="text-center">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ultimas_vendas as $venda): ?>
                                    <tr>
                                        <td>
                                            <span class="fw-semibold text-primary">#<?php echo $venda['id']; ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar me-2" style="width: 32px; height: 32px; font-size: 0.75rem;">
                                                    <?php echo strtoupper(substr($venda['cliente_nome'] ?? 'C', 0, 1)); ?>
                                                </div>
                                                <?php echo htmlspecialchars($venda['cliente_nome'] ?? 'Cliente não informado'); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="fw-semibold"><?php echo date('d/m/Y', strtotime($venda['data_venda'])); ?></div>
                                                <small class="text-muted"><?php echo date('H:i', strtotime($venda['data_venda'])); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">R$ <?php echo number_format($venda['valor_total'], 2, ',', '.'); ?></span>
                                        </td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            switch($venda['status_venda']) {
                                                case 'concluida':
                                                    $status_class = 'status-success';
                                                    $status_text = 'Concluída';
                                                    break;
                                                case 'pendente':
                                                    $status_class = 'status-warning';
                                                    $status_text = 'Pendente';
                                                    break;
                                                case 'cancelada':
                                                    $status_class = 'status-danger';
                                                    $status_text = 'Cancelada';
                                                    break;
                                                default:
                                                    $status_class = 'status-info';
                                                    $status_text = ucfirst($venda['status_venda']);
                                            }
                                            ?>
                                            <span class="status-badge <?php echo $status_class; ?>">
                                                <?php echo $status_text; ?>
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <a href="detalhes_venda.php?id=<?php echo $venda['id']; ?>"
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-4">
                        <a href="vendas.php" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i> Ver Todas as Vendas
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Sales Chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('vendasChart');
    if (ctx) {
        const vendasData = {
            labels: [
                <?php
                $labels = [];
                $valores = [];

                for ($i = 6; $i >= 0; $i--) {
                    $data = date('Y-m-d', strtotime("-$i days"));
                    $labels[] = "'" . date('d/m', strtotime($data)) . "'";

                    $valor = 0;
                    foreach ($vendas_7_dias as $venda) {
                        if ($venda['data'] == $data) {
                            $valor = $venda['total'];
                            break;
                        }
                    }
                    $valores[] = $valor;
                }

                echo implode(', ', $labels);
                ?>
            ],
            datasets: [{
                label: 'Vendas',
                data: [<?php echo implode(', ', $valores); ?>],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        };

        new Chart(ctx, {
            type: 'line',
            data: vendasData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#3b82f6',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return 'Vendas: R$ ' + context.parsed.y.toLocaleString('pt-BR', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }
});
</script>

<?php include_once 'includes/footer.php'; ?>