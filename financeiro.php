<?php
session_start();

// Verifica se o usuário está logado
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: index.php");
    exit;
}

require_once 'includes/db_connect.php';

$message = '';
$message_type = '';

// Lógica para Exclusão de Transação (apenas para transações avulsas, com cuidado)
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $transacao_id_to_delete = $_GET['id'];

    // É importante considerar se esta transação está vinculada a algo (venda, compra).
    // Por simplicidade, faremos a exclusão direta. Em um sistema real, pode-se apenas inativar.
    $sql_delete = "DELETE FROM transacoes_financeiras WHERE id = ?";

    if ($stmt_delete = $conn->prepare($sql_delete)) {
        $stmt_delete->bind_param("i", $transacao_id_to_delete);
        if ($stmt_delete->execute()) {
            $message = "Transação excluída com sucesso!";
            $message_type = "success";
        } else {
            $message = "Erro ao excluir a transação: " . $stmt_delete->error;
            $message_type = "danger";
        }
        $stmt_delete->close();
    } else {
        $message = "Erro na preparação da query de exclusão: " . $conn->error;
        $message_type = "danger";
    }
}

// Lógica para buscar todas as transações
// Podemos adicionar filtros futuramente (por tipo, categoria, data)
$sql_select_transacoes = "SELECT id, tipo, valor, data_transacao, descricao, categoria, referencia_id, tabela_referencia
                         FROM transacoes_financeiras
                         ORDER BY data_transacao DESC";
$result_transacoes = $conn->query($sql_select_transacoes);

// Cálculo do Balanço
$saldo_total = 0;
$sql_saldo = "SELECT tipo, SUM(valor) AS total_valor FROM transacoes_financeiras GROUP BY tipo";
$result_saldo = $conn->query($sql_saldo);
if ($result_saldo->num_rows > 0) {
    while($row_saldo = $result_saldo->fetch_assoc()) {
        if ($row_saldo['tipo'] == 'entrada') {
            $saldo_total += $row_saldo['total_valor'];
        } else {
            $saldo_total -= $row_saldo['total_valor'];
        }
    }
}

// Calcular estatísticas
$total_entradas = 0;
$total_saidas = 0;
$sql_stats = "SELECT tipo, SUM(valor) AS total FROM transacoes_financeiras GROUP BY tipo";
$result_stats = $conn->query($sql_stats);
if ($result_stats->num_rows > 0) {
    while($row_stats = $result_stats->fetch_assoc()) {
        if ($row_stats['tipo'] == 'entrada') {
            $total_entradas = $row_stats['total'];
        } else {
            $total_saidas = $row_stats['total'];
        }
    }
}

// Não fechar a conexão aqui pois ainda vamos usar no HTML
// $conn->close();

include_once 'includes/header.php';
?>

<!-- Page Header -->
<div class="page-header fade-in-up">
    <h1 class="page-title">
        <i class="fas fa-chart-line"></i>
        Controle Financeiro
    </h1>
    <p class="page-subtitle">
        Gerencie entradas, saídas e acompanhe o fluxo de caixa do seu negócio
    </p>
</div>

<?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show fade-in-up" role="alert">
        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : ($message_type == 'info' ? 'info-circle' : 'exclamation-triangle'); ?> me-2"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Cards de Estatísticas -->
<div class="row g-4 mb-4 fade-in-up">
    <div class="col-6 col-lg-3">
        <div class="stats-card success">
            <div class="stats-icon success">
                <i class="fas fa-arrow-up"></i>
            </div>
            <div class="stats-value">R$ <?php echo number_format($total_entradas, 2, ',', '.'); ?></div>
            <div class="stats-label">Total Entradas</div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card danger">
            <div class="stats-icon danger">
                <i class="fas fa-arrow-down"></i>
            </div>
            <div class="stats-value">R$ <?php echo number_format($total_saidas, 2, ',', '.'); ?></div>
            <div class="stats-label">Total Saídas</div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card <?php echo ($saldo_total >= 0 ? 'primary' : 'warning'); ?>">
            <div class="stats-icon <?php echo ($saldo_total >= 0 ? 'primary' : 'warning'); ?>">
                <i class="fas fa-balance-scale"></i>
            </div>
            <div class="stats-value">R$ <?php echo number_format($saldo_total, 2, ',', '.'); ?></div>
            <div class="stats-label">Saldo Atual</div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="stats-card info">
            <div class="stats-icon info">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="stats-value"><?php echo $result_transacoes->num_rows; ?></div>
            <div class="stats-label">Transações</div>
        </div>
    </div>
</div>

<!-- Tabela de Transações -->
<div class="modern-card fade-in-up">
    <div class="card-header-modern">
        <i class="fas fa-list"></i>
        Histórico de Transações
        <div class="ms-auto">
            <a href="registrar_transacao.php" class="btn btn-primary btn-sm">
                <i class="fas fa-plus me-1"></i> Nova Transação
            </a>
        </div>
    </div>
    <div class="card-body-modern">
        <div class="table-modern">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Tipo</th>
                        <th>Valor</th>
                        <th>Data</th>
                        <th>Descrição</th>
                        <th>Categoria</th>
                        <th>Ref.</th>
                        <th class="text-center">Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($result_transacoes->num_rows > 0) {
                        while($row = $result_transacoes->fetch_assoc()) {
                            $tipo_class = ($row['tipo'] == 'entrada') ? 'text-success' : 'text-danger';
                            $tipo_icon = ($row['tipo'] == 'entrada') ? 'fa-arrow-circle-down' : 'fa-arrow-circle-up'; // Ícones de entrada/saída
                            $referencia_link = 'N/A';

                            if (!empty($row['referencia_id']) && !empty($row['tabela_referencia'])) {
                                $tabela_ref = htmlspecialchars($row['tabela_referencia']);
                                $ref_id = htmlspecialchars($row['referencia_id']);
                                if ($tabela_ref == 'vendas') {
                                    $referencia_link = '<a href="detalhes_venda.php?id=' . $ref_id . '" class="text-decoration-none">Venda #' . $ref_id . '</a>';
                                } elseif ($tabela_ref == 'orcamentos') { // Se houver transações diretas de orçamento
                                    $referencia_link = '<a href="detalhes_orcamento.php?id=' . $ref_id . '" class="text-decoration-none">Orçamento #' . $ref_id . '</a>';
                                }
                                // Adicione mais condições conforme necessário para outras tabelas de referência
                            }
                            ?>
                            <tr>
                                <td><span class="text-muted">#<?php echo htmlspecialchars($row['id']); ?></span></td>
                                <td>
                                    <span class="badge bg-<?php echo ($row['tipo'] == 'entrada') ? 'success' : 'danger'; ?>">
                                        <i class="fas <?php echo $tipo_icon; ?> me-1"></i><?php echo htmlspecialchars(ucfirst($row['tipo'])); ?>
                                    </span>
                                </td>
                                <td class="<?php echo $tipo_class; ?> fw-bold">
                                    R$ <?php echo number_format($row['valor'], 2, ',', '.'); ?>
                                </td>
                                <td><?php echo date('d/m/Y H:i', strtotime($row['data_transacao'])); ?></td>
                                <td><?php echo htmlspecialchars($row['descricao']); ?></td>
                                <td>
                                    <?php if (!empty($row['categoria'])): ?>
                                        <span class="badge bg-light text-dark"><?php echo htmlspecialchars($row['categoria']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $referencia_link; ?></td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="registrar_transacao.php?id=<?php echo $row['id']; ?>" class="btn btn-outline-primary btn-sm" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="financeiro.php?action=delete&id=<?php echo $row['id']; ?>" class="btn btn-outline-danger btn-sm" title="Excluir" onclick="return confirm('Tem certeza que deseja excluir esta transação? Isso pode afetar o balanço financeiro.');">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php
                        }
                    } else {
                        echo '<tr><td colspan="8" class="text-center">Nenhuma transação financeira registrada ainda.</td></tr>';
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
$conn->close();
include_once 'includes/footer.php';
?>